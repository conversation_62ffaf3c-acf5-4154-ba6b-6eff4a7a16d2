import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:nds_app/companies/factoryFiles/home_view_factory.dart';

void main() {
  group('HomeViewFactory Tests', () {
    test('should return list of supported company', () {
      final supportedCompanies = HomeViewFactory.getSupportedCompanies();
      
      expect(supportedCompanies, contains('b2c'));
      expect(supportedCompanies, contains('lapa'));
      expect(supportedCompanies, contains('nds'));
      expect(supportedCompanies, contains('prodred'));
      expect(supportedCompanies, contains('Nichesolv'));
      expect(supportedCompanies.length, equals(5));
    });

    test('should return correct class names for each company', () {
      // Note: This test assumes the companyName is set to different values
      // In a real test environment, you might need to mock the companyName
      
      expect(HomeViewFactory.getHomeViewClassName(), isA<String>());
      expect(HomeViewFactory.getHomeViewClassName().isNotEmpty, isTrue);
    });

    test('should validate company support correctly', () {
      // This test checks if the validation method works
      // The actual result depends on the current companyName value
      expect(HomeViewFactory.isCompanySupported(), isA<bool>());
    });

    testWidgets('should create home view widget without errors', (WidgetTester tester) async {
      // Test that the factory can create a widget without throwing errors
      final homeView = HomeViewFactory.createHomeView(
        dashboardAction: () {},
      );
      
      expect(homeView, isNotNull);
      expect(homeView, isA<Widget>());
    });
  });
}
