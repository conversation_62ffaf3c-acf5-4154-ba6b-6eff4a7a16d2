import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/rider_test_details.dart';
import 'package:nds_app/repository/rider_test_repository.dart';
import 'package:nds_app/utils/concave_decoration.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/widgets/vehicle/add_ride_activity_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/screens/vehicle/ride_details_screen.dart';
import '../../common/constant.dart';
import '../../common/date_formats.dart';
import '../../constant/api_urls.dart';
import '../../constant/vehicle_status.dart';
import '../../models/trip.dart';
import '../../services/api_service.dart';
import '../../models/ride_activity.dart';

class TripHistoryList extends StatefulWidget {
  const TripHistoryList({super.key});

  @override
  State<TripHistoryList> createState() => _TripHistoryListState();
}

class _TripHistoryListState extends State<TripHistoryList> {
  List<Trip> tripHistory = [];
  bool _showAddRideActivity = false;
  Map<String, dynamic> _selectedRideDetails = {};
  final GlobalKey _todayRidesInfoKey = GlobalKey();
  Color _vehicleThemeColor = colorGrey800;
  SharedPreferences? sharedPreferences;

  // Add pagination related variables
  int _currentPage = 0;
  static const int _pageSize = 20;
  bool _isLoading = false;
  bool _hasMoreData = true;
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _initializePreferences();
    // Load initial data
    loadData();
  }

  Future<void> _initializePreferences() async {
    sharedPreferences = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        String hexColorInStr =
            sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";
        _vehicleThemeColor =
            hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (!isProdRedUser) return; // Only enable pagination for ProdRedUser

    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      if (!_isLoadingMore && _hasMoreData) {
        _loadMoreData();
      }
    }
  }

  Future<void> _loadMoreData() async {
    if (!isProdRedUser) return; // Only load more data for ProdRedUser
    if (_isLoadingMore || !_hasMoreData) return;

    _isLoadingMore = true;

    try {
      // Store current scroll position
      final currentScrollPosition = _scrollController.position.pixels;

      await loadData(isLoadMore: true);

      // Restore scroll position after loading
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(currentScrollPosition);
        }
      });
    } finally {
      _isLoadingMore = false;
    }
  }

  Future<void> loadData({bool isLoadMore = false}) async {
    if (!isLoadMore) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      if (!isLoadMore) {
        _currentPage = 0;
        tripHistory.clear();
        _hasMoreData = true;
      }

      http.Response response;

      if (isProdRedUser) {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        String imei = prefs.getString(connectedVehicleImeiNo) ?? "";
        response =
            await BackendApi.initiateGetCall(ApiUrls.tripsVehicle, params: {
          "vIdVal": imei,
          "page": _currentPage.toString(),
          "size": _pageSize.toString(),
          "sort": ''
        });
        log("===== Response: ${response.body} =====");
      } else {
        response = await BackendApi.initiateGetCall(ApiUrls.tripHistory);
        // For non-ProdRedUser, we don't need pagination
        _hasMoreData = false;
      }

      JsonDecoder decoder = const JsonDecoder();
      List<dynamic> jsonList = decoder.convert(response.body);

      if (isProdRedUser) {
        List<Trip> newTrips = jsonList.map((e) => Trip.fromJson(e)).toList();

        if (newTrips.isEmpty) {
          _hasMoreData = false;
        } else {
          setState(() {
            tripHistory.addAll(newTrips);
            _currentPage++;
          });
        }
      } else {
        setState(() {
          tripHistory = jsonList.map((e) => Trip.fromJson(e)).toList();
        });
      }
    } catch (e) {
      debugPrint("===== Error in loadData: $e =====");
    } finally {
      if (!isLoadMore) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    // If showing add ride activity screen, display it instead of trip history
    if (_showAddRideActivity && isProdRedUser) {
      // Find the Trip object for the selected ride
      Trip? selectedTrip;
      int? selectedTripIndex;

      if (_selectedRideDetails.containsKey('id')) {
        int id = _selectedRideDetails['id'];
        // Adjust for 1-based indexing in UI vs 0-based in array
        int index = id - 1;
        if (index >= 0 && index < tripHistory.length) {
          selectedTrip = tripHistory[index];
          selectedTripIndex = index;
        }
      }

      return AddRideActivityScreen(
        onRideAdded: _handleRideAdded,
        onCancel: _toggleAddRideActivity,
        rideDetails: _selectedRideDetails,
        trip: selectedTrip,
        tripIndex: selectedTripIndex,
      );
    }

    Widget widget = Center(
      child: Image.asset(
        isTwoWheels ? loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
      ),
    );

    // Check if user is disconnected and show message for ProdRedUser
    if (isProdRedUser && currentVehicleStatus == VehicleStatus.disconnected) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Text(
            insightsText['text28']!,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    if (!_isLoading) {
      widget = tripHistory.isEmpty
          ? Center(
              child: Text(
              insightsText['no_data']!,
              style: Theme.of(context).textTheme.headlineMedium,
            ))
          : SizedBox(
              height: 680 / 896 * dimensions.height,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: [
                    SizedBox(
                      height: 28 / 896 * dimensions.height,
                    ),
                    Visibility(
                        visible: isProdRedUser,
                        child: _buildTodaysRidesCard(dimensions)),
                    SizedBox(
                      height: 10 / 896 * dimensions.height,
                    ),
                    ...getTripList(dimensions),
                    SizedBox(
                      height: 80 / 896 * dimensions.height,
                    ),
                    if (_isLoadingMore)
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      ),
                    if (!_hasMoreData && tripHistory.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Center(
                          child: Text(
                            insightsText['text48']!,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            );
    }

    Column list = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Visibility(
            visible: isB2CUser && tripHistory.isNotEmpty,
            child: Column(
              children: [
                Text(
                  textAlign: TextAlign.start,
                  insightsText['text1']!,
                  style: Theme.of(context).textTheme.headlineLarge,
                ),
                SizedBox(
                  height: 12 / 896 * dimensions.height,
                ),
              ],
            )),
        widget
      ],
    );
    return list;
  }

  getTripList(Dimensions dimensions) {
    List<Container> tripList = [];
    for (int i = 0; i < tripHistory.length; i++) {
      tripList.add(getTripContainer(dimensions, tripHistory[i], i));
    }
    return tripList;
  }

  Container getTripContainer(Dimensions dimensions, Trip trip, int index) {
    DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch(trip.startTime ?? 0);
    String userStartTripTime = DateFormat(dateFormat1).format(dateTime);
    String? imageUrl;
    trip.images?.forEach(
      (element) {
        if (element.tag == defaultImageTag) {
          imageUrl = element.url;
          debugPrint("Trip history image URL: $imageUrl");
        }
      },
    );

    // Make sure we don't have empty string URLs
    if (imageUrl != null && imageUrl!.isEmpty) {
      imageUrl = null;
    }
    return Container(
      margin: EdgeInsets.only(
          bottom: 16,
          left: 6 / 414 * dimensions.width,
          right: 6 / 414 * dimensions.width),
      padding: EdgeInsets.all(12 / 414 * dimensions.width),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1, color: colorGrey200),
          boxShadow: [
            BoxShadow(
                color: colorBlack.withOpacity(0.25),
                offset: const Offset(1, 3),
                blurRadius: 3,
                spreadRadius: 1),
            BoxShadow(
                color: colorWhite.withOpacity(0.25),
                offset: const Offset(-1, -3),
                blurRadius: 3,
                spreadRadius: 1)
          ]),
      width: 374 / 414 * dimensions.width,
      height: 127 / 896 * dimensions.height,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                width: 60 / 414 * dimensions.width,
                height: 60 / 896 * dimensions.height,
                margin: EdgeInsets.only(
                  bottom: 41 / 896 * dimensions.height,
                ),
                decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(width: 1, color: colorGrey300),
                    boxShadow: [
                      BoxShadow(
                          color: colorBlack.withOpacity(0.25),
                          offset: const Offset(1, 3),
                          blurRadius: 3,
                          spreadRadius: 1),
                      BoxShadow(
                          color: colorWhite.withOpacity(0.25),
                          offset: const Offset(-1, -3),
                          blurRadius: 3,
                          spreadRadius: 1)
                    ]),
              ),
              Positioned(
                bottom: 36 / 896 * dimensions.height,
                child: Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationY(3.14),
                  child: Center(
                    child: SizedBox(
                      width: 50 / 414 * dimensions.width,
                      height: 54 / 896 * dimensions.height,
                      child: imageUrl == null
                          ? const Icon(Icons.electric_bike)
                          : CachedNetworkImage(
                              imageUrl: imageUrl!,
                              fit: BoxFit.fill,
                              progressIndicatorBuilder:
                                  (context, url, progress) => Center(
                                child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    value: progress.progress,
                                    strokeWidth: 2.0,
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) =>
                                  const Icon(Icons.electric_bike),
                            ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            width: 14 / 414 * dimensions.width,
          ),
          isProdRedUser
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${insightsText['text35']} #${index + 1}",
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                fontSize: 12,
                              ),
                        ),
                        SizedBox(height: 12 / 896 * dimensions.height),
                        Text(
                          userStartTripTime,
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontSize: 14,
                                  ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12 / 896 * dimensions.height),
                    SizedBox(
                      width: 260 / 414 * dimensions.width,
                      height: 30 / 896 * dimensions.height,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            flex: 3,
                            child: ListView(
                              scrollDirection: Axis.horizontal,
                              shrinkWrap: true,
                              physics: BouncingScrollPhysics(),
                              children: [
                                _buildInfoChip(
                                    dimensions,
                                    rideHistoryScreenImages["rideTime"]!,
                                    calculateTripDuration(
                                        trip.startTime, trip.endTime)),
                                SizedBox(width: 4 / 414 * dimensions.width),
                                _buildInfoChip(
                                    dimensions,
                                    rideHistoryScreenImages["rideDistance"]!,
                                    trip.rideDistance != null
                                        ? "${trip.rideDistance!.toStringAsFixed(2)} ${insightsText["text36"]!}"
                                        : "0 ${insightsText["text36"]!}",
                                    iconColor: colorSuccessGreen600),
                              ],
                            ),
                          ),
                          SizedBox(width: 2 / 414 * dimensions.width),
                          Expanded(
                            flex: 1,
                            child: SizedBox(
                              height: 28,
                              child: ElevatedButton(
                                onPressed: () async {
                                  if (trip.isManual == false) {
                                    _navigateToAddRideWithDetails(trip, index);
                                  } else {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => RideDetailsScreen(
                                          trip: trip,
                                          color: _vehicleThemeColor,
                                          colorType: ColorType.light,
                                        ),
                                      ),
                                    );
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: trip.isManual == true
                                      ? Colors.white
                                      : _vehicleThemeColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  elevation: 0,
                                  padding: EdgeInsets.symmetric(horizontal: 8),
                                  minimumSize: Size(36, 28),
                                  side: trip.isManual == true
                                      ? BorderSide(color: _vehicleThemeColor)
                                      : BorderSide.none,
                                ),
                                child: Text(
                                    trip.isManual == true
                                        ? insightsText["text38"]!
                                        : insightsText["text37"]!,
                                    style: poppinsTextStyle(
                                        10,
                                        trip.isManual == true
                                            ? _vehicleThemeColor
                                            : colorWhite,
                                        FontWeight.w600)),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 260 / 414 * dimensions.width,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                trip.regNo.toString(),
                                style: Theme.of(context).textTheme.displaySmall,
                              ),
                              SizedBox(
                                height: 2 / 993 * dimensions.height,
                              ),
                              Text(
                                userStartTripTime,
                                style: Theme.of(context)
                                    .textTheme
                                    .labelSmall
                                    ?.copyWith(letterSpacing: 0),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 32 / 896 * dimensions.height,
                            width: 64 / 414 * dimensions.width,
                            child:
                                Stack(alignment: Alignment.center, children: [
                              Container(
                                height: 42 / 896 * dimensions.height,
                                width: 85 / 414 * dimensions.width,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4.0),
                                  border: Border.all(
                                    color: colorGrey300,
                                    width: 1.0,
                                  ),
                                ),
                              ),
                              Container(
                                height: 32 / 896 * dimensions.height,
                                width: 64 / 414 * dimensions.width,
                                margin: const EdgeInsets.all(1.0),
                                decoration: ConcaveDecoration(
                                  alignment:
                                      MyApp.of(context).getCurrentThemeMode() ==
                                              ThemeMode.dark
                                          ? Alignment.topRight
                                          : Alignment.topLeft,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        4 / 414 * dimensions.width),
                                  ),
                                  depth: 3,
                                  colors: [
                                    colorWhite,
                                    Colors.black.withOpacity(0.25),
                                  ],
                                  opacity: 0.5,
                                ),
                              ),
                              Text(
                                dateTime.year.toString(),
                                textAlign: TextAlign.end,
                                style: Theme.of(context).textTheme.titleSmall,
                              ),
                            ]),
                          ),
                        ],
                      ),
                    ),
                    Flexible(
                      child: SizedBox(
                        height: 15 / 890 * dimensions.height,
                      ),
                    ),
                    getAddressRow(colorGreenCircle,
                        trip.startAddress.toString(), dimensions),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 3 / 414 * dimensions.width,
                        ),
                        SizedBox(
                            height: 14 / 993 * dimensions.height,
                            child: Image.asset(
                                insightsScreenImages['vertical_divider']!)),
                      ],
                    ),
                    getAddressRow(
                        colorError600, trip.endAddress.toString(), dimensions),
                  ],
                )
        ],
      ),
    );
  }

  Row getAddressRow(Color color, String address, Dimensions dimensions) {
    return Row(
      children: [
        SizedBox(
            width: 8 / 414 * dimensions.width,
            child: FittedBox(
                fit: BoxFit.fill,
                child: Icon(
                  Icons.circle,
                  color: color,
                ))),
        SizedBox(
          width: 5 / 414 * dimensions.width,
        ),
        SizedBox(
          width: 248 / 414 * dimensions.width,
          child: Text(
            address,
            overflow: TextOverflow.ellipsis,
            softWrap: false,
            style: Theme.of(context).textTheme.titleSmall,
          ),
        ),
      ],
    );
  }

  Widget _buildTodaysRidesCard(Dimensions dimensions) {
    return FutureBuilder<SharedPreferences>(
      future: SharedPreferences.getInstance(),
      builder: (context, prefSnapshot) {
        if (prefSnapshot.connectionState != ConnectionState.done ||
            !prefSnapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        String imei =
            prefSnapshot.data!.getString(connectedVehicleImeiNo) ?? "";

        return FutureBuilder<VehicleTestDetails>(
          future: VehicleTestRepository().getVehicleTestDetails(
            imei: imei.isNotEmpty ? imei : null,
          ),
          builder: (context, snapshot) {
            VehicleTestDetails testDetails =
                snapshot.hasData ? snapshot.data! : VehicleTestDetails.empty();

            return Container(
              padding: EdgeInsets.symmetric(
                horizontal: 12 / 414 * dimensions.width,
                vertical: 16 / 896 * dimensions.height,
              ),
              margin: EdgeInsets.only(
                bottom: 12,
                left: 6 / 414 * dimensions.width,
                right: 6 / 414 * dimensions.width,
              ),
              decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(width: 1, color: colorGrey200),
                  boxShadow: [
                    BoxShadow(
                        color: colorBlack.withOpacity(0.25),
                        offset: const Offset(1, 3),
                        blurRadius: 3,
                        spreadRadius: 1),
                    BoxShadow(
                        color: colorWhite.withOpacity(0.25),
                        offset: const Offset(-1, -3),
                        blurRadius: 3,
                        spreadRadius: 1)
                  ]),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        insightsText['text24']!,
                        style: Theme.of(context).textTheme.headlineLarge,
                        textAlign: TextAlign.center,
                      ),
                      GestureDetector(
                        key: _todayRidesInfoKey,
                        onTap: () {
                          _showCustomTooltip(
                            context,
                            insightsText["text45"]!,
                            _todayRidesInfoKey,
                          );
                        },
                        child: Icon(
                          Icons.info_outline,
                          size: 20,
                          color: colorGrey600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12 / 896 * dimensions.height),
                  // Stats row
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8 / 414 * dimensions.width,
                      vertical: 8 / 896 * dimensions.height,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(width: 1, color: colorGrey300),
                    ),
                    child: IntrinsicHeight(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                              child: _buildStatColumn(insightsText['text25']!,
                                  '${testDetails.rideDistance.toStringAsFixed(2)} ${insightsText["text10"] ?? "km"}')),
                          VerticalDivider(
                            thickness: 1,
                            color: colorGrey300,
                          ),
                          Expanded(
                              child: _buildStatColumn(
                            insightsText['text26']!,
                            formatDuration(testDetails.rideTime),
                          )),
                          VerticalDivider(
                            thickness: 1,
                            color: colorGrey300,
                          ),
                          Expanded(
                            child: _buildStatColumn(insightsText['text27']!,
                                '${testDetails.testRecords}'),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 8 / 896 * dimensions.height),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildStatColumn(String title, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: Center(
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontSize: 10,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).primaryTextTheme.displayMedium?.copyWith(
                  fontSize: 18,
                ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  // Add this method for building info chips
  Widget _buildInfoChip(Dimensions dimensions, String icon, String label,
      {Color? iconColor}) {
    return Container(
      height: 30,
      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: colorGrey300, width: 1.2),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            icon,
            height: 14,
            width: 14,
            fit: BoxFit.fill,
            color: iconColor,
          ),
          SizedBox(width: 1),
          Text(
            label,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontSize: 9,
                ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // Method to get formatted time in hours, minutes and seconds
  String formatDuration(double totalHours) {
    // Convert hours to total seconds
    int totalSeconds = (totalHours * 3600).round();

    int hours = totalSeconds ~/ 3600;
    int minutes = (totalSeconds % 3600) ~/ 60;
    int seconds = totalSeconds % 60;

    if (hours > 0) {
      return "$hours ${insightsText["text11"] ?? "hr"} $minutes ${insightsText["text12"] ?? "min"}";
    } else if (minutes > 0) {
      return "$minutes ${insightsText["text12"] ?? "min"} $seconds ${insightsText["text46"] ?? "sec"}";
    } else {
      return "$seconds ${insightsText["text46"] ?? "sec"}";
    }
  }

  // Helper method to format duration from milliseconds
  String formatDurationFromMillis(int? milliseconds) {
    if (milliseconds == null) return "0 ${insightsText["text46"] ?? "sec"}";
    double hours = milliseconds / (1000 * 60 * 60); // Convert ms to hours
    return formatDuration(hours);
  }

  // Helper method to calculate duration from start and end time
  String calculateTripDuration(int? startTime, int? endTime) {
    if (startTime == null || endTime == null) {
      return "0 ${insightsText["text46"] ?? "sec"}";
    }
    int durationInMillis = endTime - startTime;
    return formatDurationFromMillis(durationInMillis);
  }

  // Method to navigate to add ride activity with pre-filled details
  void _navigateToAddRideWithDetails(Trip trip, int index) {
    DateTime startTime =
        DateTime.fromMillisecondsSinceEpoch(trip.startTime ?? 0);

    Map<String, dynamic> rideDetails = {
      'id': index + 1,
      'startTime': trip.startTime,
      'time': DateFormat('h:mma').format(startTime).toLowerCase(),
      'duration': calculateTripDuration(trip.startTime, trip.endTime),
      'distance': trip.rideDistance != null
          ? "${trip.rideDistance!.toStringAsFixed(2)} ${insightsText["text36"]!}"
          : "0 ${insightsText["text36"]!}",
    };

    setState(() {
      _showAddRideActivity = true;
      _selectedRideDetails = rideDetails;
    });
  }

  // Method to toggle add ride activity view
  void _toggleAddRideActivity() {
    setState(() {
      _showAddRideActivity = !_showAddRideActivity;
      if (!_showAddRideActivity) {
        _selectedRideDetails = {};
      }
    });
  }

  // Handle when a ride is added
  void _handleRideAdded(RideActivity rideActivity) async {
    // Reload the data first
    await loadData();
    setState(() {
      // Toggle back to trip history view
      _showAddRideActivity = false;
    });
    //CustomToast.whine(insightsText['text39']!);
  }

  // Custom method to show custom tooltip popup
  void _showCustomTooltip(BuildContext context, String message, GlobalKey key) {
    final RenderBox renderBox =
        key.currentContext!.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    // Get the screen size to ensure tooltip stays on screen
    final Size screenSize = MediaQuery.of(context).size;

    final overlay = Overlay.of(context);
    OverlayEntry? entry;

    // Calculate tooltip width based on parent container
    const double tooltipWidth = 220;

    // Calculate left position to ensure tooltip doesn't go off screen
    double leftPosition =
        position.dx - tooltipWidth - 6; // Adjusted to account for arrow
    // Ensure tooltip is not off the left edge
    if (leftPosition < 10) leftPosition = 10;
    // Ensure tooltip is not off the right edge
    if (leftPosition + tooltipWidth > screenSize.width - 10) {
      leftPosition = screenSize.width - tooltipWidth - 10;
    }

    entry = OverlayEntry(
      builder: (context) => Positioned(
        top: position.dy - 40,
        left: leftPosition,
        child: Material(
          color: Colors.transparent,
          child: SizedBox(
            width: tooltipWidth,
            child: Stack(
              children: [
                Positioned(
                  child: CustomPaint(
                    painter: TooltipCustomPainter(),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(8, 8, 14, 8),
                      child: Text(
                        message,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  right: 0,
                  top: 0,
                  child: GestureDetector(
                    onTap: () {
                      entry?.remove();
                    },
                    child: Container(
                      width: 20,
                      height: 20,
                      color: Colors.transparent,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(entry);

    // Auto-dismiss after 2 seconds
    Future.delayed(const Duration(seconds: 2), () {
      entry?.remove();
    });
  }
}

class TooltipCustomPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Fill paint
    final Paint fillPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    // Border paint
    final Paint borderPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    const double arrowSize = 6;
    const double cornerRadius = 4;

    final path = Path()
      ..moveTo(cornerRadius, 0)
      ..lineTo(size.width - cornerRadius - arrowSize, 0)
      ..quadraticBezierTo(
          size.width - arrowSize, 0, size.width - arrowSize, cornerRadius)
      ..lineTo(size.width - arrowSize, size.height / 2 - arrowSize)
      ..lineTo(size.width, size.height / 2)
      ..lineTo(size.width - arrowSize, size.height / 2 + arrowSize)
      ..lineTo(size.width - arrowSize, size.height - cornerRadius)
      ..quadraticBezierTo(size.width - arrowSize, size.height,
          size.width - arrowSize - cornerRadius, size.height)
      ..lineTo(cornerRadius, size.height)
      ..quadraticBezierTo(0, size.height, 0, size.height - cornerRadius)
      ..lineTo(0, cornerRadius)
      ..quadraticBezierTo(0, 0, cornerRadius, 0);

    canvas.drawShadow(path, Colors.black.withOpacity(0.1), 1, true);
    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
