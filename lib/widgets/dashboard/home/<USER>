import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:location/location.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/nearby_vehicle.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/location_service.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/streams/nearby_poi_data.dart';
import 'package:nds_app/streams/user_location_data.dart';
import 'package:nds_app/utils/device_battery_image.dart';
import 'package:nds_app/utils/date_time_extenstion.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:ui' as ui;
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../constant/api_urls.dart';
import '../../../streams/map_and_available_vehicle_stream.dart';
import '../../../utils/toast.dart';

class MapAndAvailableVehicle extends StatefulWidget {
  const MapAndAvailableVehicle({super.key});

  @override
  State<MapAndAvailableVehicle> createState() => _MapAndAvailableVehicleState();
}

class _MapAndAvailableVehicleState extends State<MapAndAvailableVehicle> {
  final Completer<GoogleMapController> _controller =
      Completer<GoogleMapController>();
  UserLocationDataStream userLocationStream = UserLocationDataStream();
  NearbyPOIDataStream nearByPOIStream = NearbyPOIDataStream();
  final MapAndAvailableVehicleStream selectedVehicleStream =
      MapAndAvailableVehicleStream();
  late NearbyPOI nearbyPOI;
  late int selectedRange;

  Set<Marker> markers = {};
  List<NearByVehicle> nearbyVehicles = [];
  bool isNavigated = false;
  late Color? vehicleColor;
  late LatLng _point1;
  late LatLng _point2;
  double _currentZoom = 14.474599838256836;
  Timer? _zoomLogTimer;
  LogScreenTrackingEvent logScreenTrackingEvent = LogScreenTrackingEvent();

  @override
  void initState() {
    _point1 = const LatLng(0, 0);
    _point2 = const LatLng(0, 0);

    nearbyPOI = currentNearbyPOI;
    markers = currentMarkers;
    loadColorFromSharedPreferences();
    userLocationStream.userLocationData.listen((event) {
      userLocationData = event;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return FutureBuilder(
      builder: (context, snapshot) {
        Widget widget = Center(
          child: Image.asset(
            isTwoWheels
                ? loaderGifImages['2Wheels']!
                : loaderGifImages['3Wheels']!,
          ),
        );
        if (snapshot.connectionState == ConnectionState.done) {
          widget = Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Visibility(
                visible: !isB2CUser,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      homeScreenText["text2"]!,
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    Text(
                      homeScreenText["text18"]!
                          .replaceAll("@range", selectedRange.toString()),
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 12 / 896 * dimensions.width,
              ),
              Stack(
                children: [
                  Container(
                    height: 380 / 896 * dimensions.height,
                    width: 374 / 414 * dimensions.width,
                    decoration: BoxDecoration(
                        border: Border.all(width: 1, color: colorGrey300),
                        borderRadius: BorderRadius.circular(8.0),
                        boxShadow: [
                          BoxShadow(
                              color: MyApp.of(context).getCurrentThemeMode() ==
                                      ThemeMode.dark
                                  ? colorGrey500.withOpacity(0.7)
                                  : colorGrey300.withOpacity(0.7),
                              offset: const Offset(3, 3),
                              spreadRadius: 2,
                              blurRadius: 5)
                        ]),
                    child: StreamBuilder<NearbyPOI>(
                        stream: nearByPOIStream.nearbyPOI,
                        builder: (context, snapshot) {
                          if (snapshot.data != null) {
                            nearbyPOI = snapshot.data!;
                          }
                          return ClipRRect(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(8)),
                            child: GoogleMap(
                              mapType: MapType.normal,
                              zoomControlsEnabled: false,
                              myLocationButtonEnabled: false,
                              initialCameraPosition: CameraPosition(
                                target: LatLng(userLocationData?.latitude ?? 0,
                                    userLocationData?.longitude ?? 0),
                                zoom: _currentZoom,
                              ),
                              myLocationEnabled: true,
                              markers: markers,
                              onMapCreated: (GoogleMapController controller) {
                                _controller.complete(controller);
                              },
                              onCameraMove: (position) {
                                _onCameraMove(position);
                              },
                              gestureRecognizers: <Factory<
                                  OneSequenceGestureRecognizer>>{
                                Factory<OneSequenceGestureRecognizer>(
                                  () => EagerGestureRecognizer(),
                                ),
                              },
                            ),
                          );
                        }),
                  ),
                  Positioned(
                    bottom: 8,
                    right: 16,
                    child: Column(
                      children: [
                        Material(
                          elevation: 4,
                          shape: const CircleBorder(),
                          child: IconButton(
                            onPressed: () async {
                              GoogleMapController controller =
                                  await _controller.future;
                              await controller.animateCamera(
                                  CameraUpdate.newCameraPosition(CameraPosition(
                                target: LatLng(userLocationData?.latitude ?? 0,
                                    userLocationData?.longitude ?? 0),
                                zoom: 14.4746,
                              )));
                              nearByPOIStream
                                  .updateNearbyVehiclesResponse(nearbyPOI);
                            },
                            icon: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(Icons.my_location_outlined),
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        const SizedBox(height: 8),
                        StreamBuilder<NearByVehicle?>(
                          stream: selectedVehicleStream.selectedVehicle,
                          builder: (context, snapshot) {
                            if (snapshot.hasData && snapshot.data != null) {
                              return Material(
                                color: const Color(0xff3871E1),
                                elevation: 4,
                                shape: const CircleBorder(),
                                child: CustomButton.icon(
                                  onPressed: () {
                                    openGoogleMaps(
                                      userLat: userLocationData?.latitude ?? 0,
                                      userLong:
                                          userLocationData?.longitude ?? 0,
                                      destLat: snapshot.data?.latitude ?? 0,
                                      destLong: snapshot.data?.longitude ?? 0,
                                    );
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.directions,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 16 / 896 * dimensions.height,
              ),
              StreamBuilder(
                stream: nearByPOIStream.nearbyPOI,
                builder: (context, snapshot) {
                  Widget widget = Center(
                    child: Image.asset(
                      isTwoWheels
                          ? loaderGifImages['2Wheels']!
                          : loaderGifImages['3Wheels']!,
                    ),
                  );
                  if (snapshot.data != null) {
                    nearbyPOI = snapshot.data!;
                    widget = getAvailableVehicles(dimensions, nearbyPOI);
                  } else if (nearbyPOI.nearByVehicles != null) {
                    widget = getAvailableVehicles(dimensions, nearbyPOI);
                  }
                  return widget;
                },
              ),
              SizedBox(
                height: 100 / 896 * dimensions.height,
              ),
            ],
          );
        }
        return widget;
      },
      future: getNearbyVehicles(),
    );
  }

  void _onCameraMove(CameraPosition position) {
    if (_currentZoom != position.zoom) {
      _currentZoom = position.zoom;
      _zoomLogTimer?.cancel();
      _zoomLogTimer = Timer(const Duration(seconds: 1), () {
        logScreenTrackingEvent.logScreenView(
            eventName: trackingLabels['ZoomMapAction']!);
      });
    }
  }

  void _setMapFitToBounds() async {
    final LatLngBounds bounds = LatLngBounds(
      southwest: LatLng(
        _point1.latitude < _point2.latitude
            ? _point1.latitude
            : _point2.latitude,
        _point1.longitude < _point2.longitude
            ? _point1.longitude
            : _point2.longitude,
      ),
      northeast: LatLng(
        _point1.latitude > _point2.latitude
            ? _point1.latitude
            : _point2.latitude,
        _point1.longitude > _point2.longitude
            ? _point1.longitude
            : _point2.longitude,
      ),
    );
    GoogleMapController controller = await _controller.future;

    controller.moveCamera(CameraUpdate.newLatLngBounds(bounds, 50));
  }

  getNearbyVehicles() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    selectedRange = pref.getInt(vehicleVisibilityRangeKey) ?? 10;
    userLocationData = userLocationData ?? await determineLocation();

    _point1 = LatLng(
        userLocationData!.latitude ?? 0, userLocationData!.longitude ?? 0);
    userLocationStream.updateUserlocationDataResponse(userLocationData!);
    refreshNearByPOIData();
  }

  setMarkers(List<NearByVehicle> nearbyVehicles) async {
    final Uint8List disconnectedVehicleMarkerIcon =
        await getBytesFromAsset(googleMapImages['marker_red']!, null);
    for (NearByVehicle vehicle in nearbyVehicles) {
      Uint8List? connectedVehicleMarkerIcon;
      if (vehicleInfoConstant != null &&
          vehicleInfoConstant?.regNo == (vehicle.regNo ?? "")) {
        connectedVehicleMarkerIcon =
            await getBytesFromAsset(googleMapImages['marker_green']!, null);
      }

      markers.add(Marker(
        icon: BitmapDescriptor.bytes(
            connectedVehicleMarkerIcon ?? disconnectedVehicleMarkerIcon,
            width: 48,
            height: 48),
        markerId: MarkerId(vehicle.regNo ?? DateTime.now().toString()),
        position: LatLng(vehicle.latitude ?? 0, vehicle.longitude ?? 0),
        infoWindow: InfoWindow(
          title: vehicle.regNo ?? "",
        ),
      ));
    }
    currentMarkers = markers;
  }

  Future<Uint8List> getBytesFromAsset(String path, int? width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  getAvailableVehicles(Dimensions dimensions, NearbyPOI nearbyPOI) {
    nearbyVehicles = nearbyPOI.nearByVehicles ?? [];

    return ListView.builder(
      padding: EdgeInsets.zero,
      physics: const BouncingScrollPhysics(),
      shrinkWrap: true,
      itemCount: nearbyVehicles.length,
      itemBuilder: (context, index) {
        NearByVehicle vehicleDetails = nearbyVehicles[index];
        DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(
            vehicleDetails.locationRecordedTime ?? 0);

        String? imageUrl;

        vehicleDetails.images?.forEach(
          (element) {
            if (element.tag == defaultImageTag) {
              imageUrl = element.url;
            }
          },
        );
        if (isB2CUser &&
            vehicleInfoConstant != null &&
            vehicleInfoConstant?.regNo == (vehicleDetails.regNo ?? "")) {
          // Call the stream function at the beginning
          selectedVehicleStream.updateSelectedVehicle(vehicleDetails);
        }
        return GestureDetector(
          onTap: () async {
            selectedVehicleStream.updateSelectedVehicle(vehicleDetails);
            GoogleMapController controller = await _controller.future;
            await controller
                .animateCamera(CameraUpdate.newCameraPosition(CameraPosition(
              target: LatLng(
                  vehicleDetails.latitude ?? (userLocationData?.latitude ?? 0),
                  vehicleDetails.longitude ??
                      (userLocationData?.longitude ?? 0)),
              zoom: 14.4746,
            )));
            nearByPOIStream.updateNearbyVehiclesResponse(nearbyPOI);
          },
          child: Column(
            children: [
              Container(
                height: 72 / 896 * dimensions.height,
                padding: EdgeInsets.symmetric(
                    horizontal: 12 / 414 * dimensions.width),
                decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: MyApp.of(context).getCurrentThemeMode() ==
                                ThemeMode.dark
                            ? [colorGrey800, colorBlack]
                            : [colorGrey200, colorWhite],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight),
                    borderRadius: BorderRadius.all(
                        Radius.circular(8 / 414 * dimensions.width)),
                    border: Border.all(
                        color: (vehicleInfoConstant != null &&
                                vehicleInfoConstant?.regNo ==
                                    (vehicleDetails.regNo ?? ""))
                            ? colorGrey600
                            : colorGrey300),
                    boxShadow: [
                      BoxShadow(
                          color: MyApp.of(context).getCurrentThemeMode() ==
                                  ThemeMode.dark
                              ? colorGrey500.withOpacity(0.7)
                              : colorGrey300.withOpacity(0.7),
                          offset: const Offset(3, 3),
                          spreadRadius: 2,
                          blurRadius: 5)
                    ]),
                child: Row(
                  children: [
                    SizedBox(
                      width: 50 / 414 * dimensions.width,
                      child: imageUrl != null
                          ? CachedNetworkImage(
                              imageUrl: imageUrl!,
                              fit: BoxFit.contain,
                              memCacheWidth: 150,
                              maxWidthDiskCache: 150,
                              fadeInDuration: const Duration(milliseconds: 100),
                              placeholder: (context, url) => const SizedBox(
                                width: 20,
                                height: 20,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2.0),
                              ),
                              errorWidget: (context, url, error) => const Icon(
                                  Icons.electric_bike,
                                  size: 50,
                                  color: Colors.grey),
                            )
                          : const Icon(Icons.electric_bike,
                              size: 50, color: Colors.grey),
                    ),
                    SizedBox(
                      width: 8 / 414 * dimensions.width,
                    ),
                    SizedBox(
                      width: 285 / 414 * dimensions.width,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                vehicleDetails.regNo ?? "",
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              getBatteryPercentageContainer(dimensions,
                                  vehicleDetails.charge ?? 0, context),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "${vehicleDetails.distance ?? 0} ${vehicleDetails.distanceUnit}",
                                style: poppinsTextStyle(
                                    12 / 414 * dimensions.width,
                                    colorGrey400,
                                    FontWeight.w300),
                              ),
                              Visibility(
                                visible: (vehicleInfoConstant != null &&
                                    vehicleInfoConstant?.regNo ==
                                        (vehicleDetails.regNo ?? "")),
                                child: Text(
                                  homeScreenText['connected_label']!,
                                  style: poppinsTextStyle(
                                      12 / 414 * dimensions.width,
                                      colorGreenSuccess,
                                      FontWeight.w300),
                                ),
                              ),
                              Text(
                                dateTime.timeAgo(numericDates: true),
                                style: poppinsTextStyle(
                                    12 / 414 * dimensions.width,
                                    colorGrey400,
                                    FontWeight.w300),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12 / 896 * dimensions.height)
            ],
          ),
        );
      },
    );
  }

  loadColorFromSharedPreferences() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    String hexColorInStr = pref.getString(vehicleThemeColorInHex) ?? "";
    vehicleColor =
        hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
  }

  Future<void> refreshNearByPOIData() async {
    JsonDecoder decoder = const JsonDecoder();

    while (!isNavigated) {
      try {
        // Get user location
        userLocationData = await determineLocation();

        // Validate location data
        double? latitude = userLocationData?.latitude;
        double? longitude = userLocationData?.longitude;

        // Additional validation to prevent 0 values
        if (latitude == null ||
            latitude == 0 ||
            longitude == null ||
            longitude == 0) {
          //CustomToast.error('Invalid location detected, using default coordinates');
          // Use fallback coordinates
          latitude = 28.612894;
          longitude = 77.229446;

          // Update userLocationData with valid coordinates
          userLocationData = LocationData.fromMap({
            'latitude': latitude,
            'longitude': longitude,
            'accuracy': 0.0,
            'altitude': 0.0,
            'speed': 0.0,
            'speed_accuracy': 0.0,
            'heading': 0.0,
            'time': DateTime.now().millisecondsSinceEpoch
          });
        }

        userLocationStream.updateUserlocationDataResponse(userLocationData!);

        // Get nearby vehicles with validated coordinates
        http.Response nearByVehiclesResponse = await BackendApi.initiateGetCall(
            ApiUrls.availableVehicles,
            params: {
              "latitude": latitude,
              "longitude": longitude,
              "range": selectedRange
            });

        // Parse response
        Map<String, dynamic> nearByVehiclesDecoded =
            decoder.convert(nearByVehiclesResponse.body);
        nearbyPOI = NearbyPOI.fromJson(nearByVehiclesDecoded);

        // Sort and reorganize nearby vehicles
        if (nearbyPOI.nearByVehicles != null) {
          nearbyPOI.nearByVehicles
              ?.sort((a, b) => b.charge!.compareTo(a.charge!));

          List<NearByVehicle> nearByVehicles = [];
          nearbyPOI.nearByVehicles?.forEach((element) {
            if (vehicleInfoConstant != null &&
                vehicleInfoConstant?.regNo == (element.regNo ?? "")) {
              nearByVehicles.insert(0, element);
            } else {
              nearByVehicles.add(element);
            }
          });
          nearbyPOI.nearByVehicles = nearByVehicles;
        } else {
          // Handle case when there are no nearby vehicles
          nearbyPOI.nearByVehicles = [];
        }

        currentNearbyPOI = nearbyPOI;

        // Update markers
        markers.clear();
        if (nearbyPOI.nearByVehicles != null &&
            nearbyPOI.nearByVehicles!.isNotEmpty) {
          await setMarkers(nearbyPOI.nearByVehicles!);
        }

        nearByPOIStream.updateNearbyVehiclesResponse(nearbyPOI);

        // Set map bounds if needed
        List<NearByVehicle> nearByVehicles = nearbyPOI.nearByVehicles ?? [];
        if (_point2.latitude == 0 &&
            _point2.longitude == 0 &&
            nearByVehicles.isNotEmpty) {
          // Use the first vehicle's location, ensuring it's not null or zero
          double vehicleLat = nearByVehicles.first.latitude ?? 0;
          double vehicleLng = nearByVehicles.first.longitude ?? 0;

          // If vehicle location is invalid, use user location
          if (vehicleLat == 0 || vehicleLng == 0) {
            vehicleLat = latitude;
            vehicleLng = longitude;
          }

          _point2 = LatLng(vehicleLat, vehicleLng);
          _setMapFitToBounds();
        }
      } catch (e) {
        // CustomToast.error('Error refreshing nearby POIs: $e');
      }

      // Wait before next refresh
      await Future.delayed(const Duration(seconds: 10));
    }
  }

  void openGoogleMaps({
    required double userLat,
    required double userLong,
    required double destLat,
    required double destLong,
  }) async {
    final googleMapsUrl =
        'https://www.google.com/maps/dir/?api=1&origin=$userLat,$userLong&destination=$destLat,$destLong&travelmode=driving';

    final Uri url = Uri.parse(googleMapsUrl);
    if (await canLaunchUrl(url)) {
      try {
        await launchUrl(url);
      } catch (e) {
        CustomToast.error('Failed to open Google Maps: ${e.toString()}');
      }
    } else {
      CustomToast.error(
          'Google Maps app is not installed or not supported on this device.');
    }
  }

  @override
  void dispose() {
    isNavigated = true;
    _zoomLogTimer?.cancel();
    super.dispose();
  }
}
