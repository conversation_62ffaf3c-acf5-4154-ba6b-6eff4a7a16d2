import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/streams/vehicle_status_data.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../common/constant.dart';
import '../../common/dimensions.dart';
import '../../common/image_urls.dart';
import '../../common/shared_preferences_keys.dart';
import '../../constant/action.dart';
import '../../constant/api_urls.dart';
import '../../models/user_info.dart';
import '../../models/vehicle_info.dart';
import '../../services/api_service.dart';

/// Base home view widget that provides common functionality for all company home views
/// This contains the shared data loading logic and state management
abstract class BaseHomeView extends StatefulWidget {
  final Function dashboardAction;
  final String companyName;
  
  const BaseHomeView({
    super.key,
    required this.dashboardAction,
    required this.companyName,
  });
}

abstract class BaseHomeViewState<T extends BaseHomeView> extends State<T> {
  VehicleInfo vehicleInfo = VehicleInfo();
  late String homeUserDisplayName;
  late String imageUrl;
  late ScrollController _scrollController;
  VehicleStatusDataStream vehicleStatusDataStream = VehicleStatusDataStream();
  VehicleDataStream vehicleDataStream = VehicleDataStream();
  Timer? _scrollLogTimer;
  LogScreenTrackingEvent logScreenTrackingEvent = LogScreenTrackingEvent();

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_screen': '${widget.companyName} Home Screen',
      'screen_class': widget.runtimeType.toString(),
    });
    imageUrl = "";
    homeUserDisplayName = homeDisplayNameOptions[0];

    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    super.initState();
  }

  _scrollListener() {
    if (_scrollLogTimer?.isActive ?? false) return;
    _scrollLogTimer = Timer(const Duration(seconds: 1), () {
      logScreenTrackingEvent.logScreenView(
          eventName: trackingLabels['ScrollHomeScreenAction']!);
    });
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return FutureBuilder(
      builder: (context, snapshot) {
        Widget widget = Center(
          child: Image.asset(
            isTwoWheels
                ? loaderGifImages['2Wheels']!
                : loaderGifImages['3Wheels']!,
          ),
        );

        if (snapshot.connectionState == ConnectionState.done) {
          widget = SizedBox(
            height: 775 / 896 * dimensions.height,
            child: Scrollbar(
              controller: _scrollController,
              radius: const Radius.circular(20),
              thickness: 5,
              child: SingleChildScrollView(
                controller: _scrollController,
                scrollDirection: Axis.vertical,
                child: buildHomeContent(context, dimensions),
              ),
            ),
          );
        }
        return widget;
      },
      future: loadData(),
    );
  }

  /// Abstract method that each company implementation must override
  /// to provide their specific home screen content
  Widget buildHomeContent(BuildContext context, Dimensions dimensions);

  /// Common data loading logic shared across all companies
  loadData() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    JsonDecoder decoder = const JsonDecoder();

    if (userInfo == null) {
      userInfo = await getUserInfo(decoder);
      userName = userInfo?.firstName ?? "";

      if (userInfo?.connectedVehicleImei != null) {
        pref.setString(
            connectedVehicleImeiNo, userInfo?.connectedVehicleImei! ?? "");
        currentVehicleStatus = VehicleStatus.connected;
        int statusCode = await DialogAction.vehicleInfo
            .action(imei: userInfo?.connectedVehicleImei);

        if (statusCode != 200) {
          isVehicleInfoAlertMessageExist = true;
        }

        vehicleStatusDataStream
            .updateVehicleStatusResponse(VehicleStatus.connected);
        if (vehicleInfoConstant != null) {
          vehicleInfo = vehicleInfoConstant ?? VehicleInfo();
        } else {
          vehicleInfo = VehicleInfo();
        }
        vehicleDataStream.updateVehicleInfo(vehicleInfo);
      }
    } else {
      if (userInfo?.connectedVehicleImei != "" &&
          userInfo?.connectedVehicleImei != null) {
        await DialogAction.vehicleInfo
            .action(imei: userInfo?.connectedVehicleImei);
      }
      
      // Company-specific user info handling
      await handleUserInfoRefresh(decoder);
    }
    
    homeUserDisplayName =
        pref.getString(homeDisplayNameKey) ?? homeDisplayNameOptions[0];

    vehicleInfo.images?.forEach(
      (element) {
        if (element.tag == defaultImageTag) {
          imageUrl = element.url!;
        }
      },
    );
  }

  /// Override this method for company-specific user info refresh logic
  Future<void> handleUserInfoRefresh(JsonDecoder decoder) async {
    // Default implementation - can be overridden by companies
    if (vehicleInfoConstant != null) {
      vehicleInfo = vehicleInfoConstant!;
      vehicleDataStream.updateVehicleInfo(vehicleInfo);
    }
  }

  Future<UserInfo?> getUserInfo(JsonDecoder decoder) async {
    http.Response userInfoResponse =
        await BackendApi.initiateGetCall(ApiUrls.userInfo);
    Map<String, dynamic> userResponse = decoder.convert(userInfoResponse.body);
    return UserInfo.fromJson(userResponse);
  }

  @override
  void dispose() {
    _scrollLogTimer?.cancel();
    super.dispose();
  }
}
