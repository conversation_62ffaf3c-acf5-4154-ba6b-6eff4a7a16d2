import 'package:firebase_analytics/firebase_analytics.dart';
import 'dart:ui';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_blocs.dart';
import 'package:nds_app/blocs/insight/toggle/insight_toggle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/relation/select_relation_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_rider/user_rider_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/vehicle_toogle_bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_details_bloc.dart';
import 'package:nds_app/common/dark_theme.dart';
import 'package:nds_app/common/light_theme.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/company/factoryFiles/check_app_update_screen_factory.dart';
import 'package:nds_app/company/factoryFiles/splash_screen_factory.dart';
import 'package:nds_app/services/firebase_remote_config_serivce.dart';
import 'package:nds_app/services/package_info_setup.dart';

import 'firebase_options.dart';

ValueNotifier<bool> isBottomSheetOpenNotifier = ValueNotifier<bool>(false);
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  if (const String.fromEnvironment('env', defaultValue: 'dev') == 'prod') {
    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };
    // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
  }

  final packageInfo = PackageInfoSetup();
  await packageInfo.initialize();
  final firebaseRemoteConfigService = FirebaseRemoteConfigService();
  await firebaseRemoteConfigService.initialize();
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitDown,
    DeviceOrientation.portraitUp,
  ]);

  // Initialize cached network image with optimized default settings
  PaintingBinding.instance.imageCache.maximumSizeBytes =
      1024 * 1024 * 100; // 100 MB

  runApp(const MyApp());
}

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

Future<void> initializeNotifications() async {
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');
  const DarwinInitializationSettings darwinInitializationSettings =
      DarwinInitializationSettings();
  const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: darwinInitializationSettings);
  await flutterLocalNotificationsPlugin.initialize(initializationSettings);

  // Create a notification channel
  const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'max_importance_channel_id', // Provide a unique channel ID
    'Channel Name', // Provide a name for the channel
    importance: Importance.max,
  );
  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await initializeNotifications();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();

  // ignore: library_private_types_in_public_api
  static _MyAppState of(BuildContext context) =>
      context.findAncestorStateOfType<_MyAppState>()!;
}

class _MyAppState extends State<MyApp> {
  ThemeMode _themeMode = ThemeMode.light;
  late ThemeData _themeData;
  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
      FirebaseAnalyticsObserver(analytics: analytics);
  @override
  Widget build(BuildContext context) {
    final MediaQueryData data = MediaQuery.of(context);

    return MultiBlocProvider(
      providers: [
        BlocProvider<InsightToggleBloc>(
          create: (BuildContext context) => InsightToggleBloc(),
        ),
        BlocProvider<VehicleToogleBloc>(
          create: (BuildContext context) => VehicleToogleBloc(),
        ),
        BlocProvider<StatisticsBloc>(
          create: (BuildContext context) => StatisticsBloc(),
        ),
        BlocProvider<VehicleDetailsBloc>(
          create: (BuildContext context) => VehicleDetailsBloc(),
        ),
        BlocProvider<ConnectVehicleToogleBloc>(
          create: (BuildContext context) => ConnectVehicleToogleBloc(),
        ),
        BlocProvider<InternetConnectivityBloc>(
          create: (BuildContext context) => InternetConnectivityBloc(),
        ),
        BlocProvider<SelectRelationBloc>(
          create: (BuildContext context) => SelectRelationBloc(),
        ),
        BlocProvider<EditRiderDropDownBloc>(
          create: (BuildContext context) => EditRiderDropDownBloc(),
        ),
        BlocProvider<UserRiderBloc>(
          create: (BuildContext context) => UserRiderBloc(),
        ),
        BlocProvider<UserVehicleBloc>(
          create: (BuildContext context) => UserVehicleBloc(),
        ),
      ],
      child: MaterialApp(
        navigatorObservers: [observer],
        builder: (BuildContext context, Widget? child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(
                textScaler: data.textScaler
                    .clamp(minScaleFactor: 0, maxScaleFactor: 1.2)),
            child: child!,
          );
        },
        title: commonText['appTitle']!,
        theme: LightTheme.themeData(context),
        darkTheme: DarkTheme.themeData(context),
        themeMode: _themeMode,
        home: const String.fromEnvironment('env', defaultValue: 'dev') == 'prod'
            ? CheckAppUpdateScreenFactory.createCheckAppUpdateScreen()
            : SplashScreenFactory.createSplashScreen(),
      ),
    );
  }

  onRebuild() {
    setState(() {});
  }

  void changeTheme(ThemeMode themeMode) {
    setState(() {
      _themeMode = themeMode;
    });
  }

  ThemeMode getCurrentThemeMode() {
    return _themeMode;
  }

  ThemeData getThemeData() {
    if (_themeMode == ThemeMode.dark) {
      _themeData = DarkTheme.themeData(context);
    } else {
      _themeData = LightTheme.themeData(context);
    }
    return _themeData;
  }
}
