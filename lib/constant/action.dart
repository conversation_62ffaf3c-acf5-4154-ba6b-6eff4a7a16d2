import 'dart:convert';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:location/location.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_event.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/models/enums/alert_type.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/company/factoryFiles/dashboard_factory.dart';
import 'package:nds_app/screens/splashScreen/PromotionalScreen/promotional_screen.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../common/constant.dart';
import '../constant/api_urls.dart';
import '../models/vehicle_info.dart';
import '../services/api_service.dart';
import '../utils/toast.dart';

enum DialogAction {
  connect(connectAction, "Connect to Vehicle", "Last four digit of IMEI"),
  disconnect(disconnectAction, "Disconnect Vehicle", ""),

  vehicleInfo(vehicleInfoAction, "", ""),
  logout(logoutAction, "Logout", "Are you sure want to logout?"),
  deleteUserAccount(deleteUserAccountAction, "Delete",
      "Are you sure want to delete your account?");

  final Function(
      {String? chassisNumber,
      String? imei,
      String code,
      BuildContext? context}) action;

  final String title;

  final String content;

  const DialogAction(this.action, this.title, this.content);
}

Future<String?> connectAction(
    {String? chassisNumber,
    String? imei,
    String? code,
    BuildContext? context}) async {
  final pref = await SharedPreferences.getInstance();
  String? message;
  code = code ?? "";
  LocationData? locationData = userLocationData;

  Map<String, dynamic> request = {
    "latitude": locationData?.latitude,
    "longitude": locationData?.longitude
  };

  if (code.length == 4) {
    request.addAll({"code": code});
  } else if (imei == null) {
    request.addAll({"chassisNumber": code});
  } else {
    request.addAll({"code": imei});
  }
  // ignore: use_build_context_synchronously
  getCircularProgressIndicator(context!);

  try {
    // Add a timeout to the API call to prevent indefinite waiting
    http.Response response =
        await BackendApi.initiatePostCall(ApiUrls.connectVehicle, body: request)
            .timeout(const Duration(seconds: 120), // 2 minute timeout
                onTimeout: () {
      throw TimeoutException("Connection timeout");
    });

    JsonDecoder decoder = const JsonDecoder();

    if (response.statusCode == 200) {
      LogScreenTrackingEvent()
          .logScreenView(eventName: trackingLabels['ConnectAction']!);
      var res = decoder.convert(response.body);
      pref.setString(connectedVehicleImeiNo, res["imei"]);
      pref.remove(tripMeterStartValueKey);
      pref.remove(isTripMeterStoppedKey);
      currentVehicleStatus = VehicleStatus.connected;

      await DialogAction.vehicleInfo.action();

      CustomToast.message(
          toastMessageText['text1']!.replaceAll('@code', res["regNo"]));

      int count = 0;

      // ignore: use_build_context_synchronously
      if (context.mounted) {
        await Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
                builder: (context) => DashboardFactory.createDashboard()),
            (r) => count++ >= 2);
      }
    } else if ([404, 409, 400].contains(response.statusCode)) {
      var res = decoder.convert(response.body);
      message = res['message'];
      debugPrint("connect message : {$message}");
    }
  } catch (e) {
    debugPrint("Connection error: $e");
    message = e.toString();
    if (message.contains("TimeoutException")) {
      message = "Connection timed out. Please try again.";
    }
  } finally {
    // Close the progress dialog if it's still open
    // ignore: use_build_context_synchronously
    if (context.mounted && Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  return message;
}

disconnectAction(
    {String? chassisNumber,
    String? imei,
    String? code,
    BuildContext? context}) async {
  getCircularProgressIndicator(context!);

  http.Response response = await BackendApi.initiatePostCall(
    ApiUrls.disconnectVehicle,
  );
  if (response.statusCode == 200) {
    LogScreenTrackingEvent()
        .logScreenView(eventName: trackingLabels['DisconnectAction']!);
    final pref = await SharedPreferences.getInstance();
    pref.setString(connectedVehicleImeiNo, "");
    pref.remove(vehicleThemeColorInHex);
    pref.remove(vehicleThemeColorTypeKey);
    pref.remove(tripMeterStartValueKey);
    pref.remove(isTripMeterStoppedKey);

    currentVehicleStatus = VehicleStatus.disconnected;
    vehicleInfoConstant = null;
    currentTempAlertType = TempAlertType.none;
    isTempAlertMessageExist = false;

    JsonDecoder decoder = const JsonDecoder();
    var res = decoder.convert(response.body);

    // ignore: use_build_context_synchronously
    Navigator.pop(context);

    CustomToast.message(
        toastMessageText['text3']!.replaceAll('@code', res["regNo"]));

    int count = 0;
    if (isB2CUser) {
      // ignore: use_build_context_synchronously
      context.read<UserVehicleBloc>().add(LoadUserVehicleEvent());
      // ignore: use_build_context_synchronously
      List<Rider> riders = context.read<UserVehicleBloc>().state.riders;
      Rider? owner;
      for (Rider rider in riders) {
        if (rider.isOwner) {
          owner = rider;
          pref.setString(connectedVehicleImeiNo, rider.imei);

          currentVehicleStatus = VehicleStatus.connected;
          await DialogAction.vehicleInfo.action(imei: rider.imei);

          CustomToast.message(
              toastMessageText['text1']!.replaceAll('@code', rider.regNo));
          int count = 0;

          await Navigator.pushAndRemoveUntil(
              // ignore: use_build_context_synchronously
              context,
              MaterialPageRoute(
                  builder: (context) => DashboardFactory.createDashboard()),
              (r) => count++ >= 2);
        }
      }
      if (owner == null) {
        // ignore: use_build_context_synchronously
        await Navigator.pushAndRemoveUntil(
            // ignore: use_build_context_synchronously
            context,
            MaterialPageRoute(builder: (context) => const PromotionalScreen()),
            (r) => count++ >= 3);
      }
    } else {
      // ignore: use_build_context_synchronously
      await Navigator.pushAndRemoveUntil(
          // ignore: use_build_context_synchronously
          context,
          MaterialPageRoute(
              builder: (context) => isB2CUser
                  ? const PromotionalScreen()
                  : DashboardFactory.createDashboard()),
          (r) => count++ >= (isB2CUser ? 3 : 2));
    }
  }

  return response.statusCode;
}

vehicleInfoAction(
    {String? chassisNumber,
    String? imei,
    String? code,
    BuildContext? context}) async {
  JsonDecoder decoder = const JsonDecoder();
  final pref = await SharedPreferences.getInstance();
  String imei = pref.getString(connectedVehicleImeiNo) ?? "";
  VehicleDataStream stream = VehicleDataStream();
  http.Response vehicleInfoResponse = await BackendApi.initiateGetCall(
      ApiUrls.vehicleInfo,
      params: {"imei": imei});
  if (vehicleInfoResponse.statusCode == 200) {
    Map<String, dynamic> vehicleResponse =
        decoder.convert(vehicleInfoResponse.body);
    vehicleInfoConstant = VehicleInfo.fromJson(vehicleResponse);
    if (vehicleInfoConstant?.currentDriveMode?.toLowerCase() ==
        'null_drive_selection') {
      vehicleInfoConstant?.currentDriveMode = 'eco';
    }
    stream.updateVehicleInfo(vehicleInfoConstant!);
    isVehicleInfoAlertMessageExist = false;
  } else {
    vehicleInfoConstant = VehicleInfo(
      charge: null,
      batteryCharging: false,
      batteryConnected: true,
      remainingTimeForCharge: 0,
      regNo: "",
      modelNo: "",
      color: "",
      imageUrls: null,
      currentDriveMode: "ECO",
      totalDistance: 0,
      batteryTemperature: 0,
      temperatureUnit: "C",
      batteryVoltage: 0,
      vehicleModeInfoList: [],
      aiVinRecordedTime: null,
      current: 0,
      motorDcCurrents: [],
    );
    isVehicleInfoAlertMessageExist = true;
  }

  String themeColor = pref.getString(vehicleThemeColorInHex) ?? "";

  if (vehicleInfoConstant?.color != themeColor || themeColor.isEmpty) {
    pref.setString(vehicleThemeColorInHex, vehicleInfoConstant?.color ?? "");
    pref.setString(vehicleThemeColorTypeKey,
        vehicleInfoConstant?.colorType.toString() ?? "normal");
  }
  return vehicleInfoResponse.statusCode;
}

logoutAction(
    {String? chassisNumber,
    BuildContext? context,
    String? imei,
    String? code}) async {
  final request = {
    "activityType": ActivityType.logout.requestName,
    "value": "",
    "organisationId": organisationId
  };
  http.Response response = await BackendApi.initiatePostCall(
    ApiUrls.saveUserKeyActivity,
    body: request,
  );

  if (response.statusCode != 200) {
    debugPrint("user activity type ${ActivityType.logout.name} failed");
  }
  await BackendApi.initiateGetCall(
    ApiUrls.authLogout,
  );
  if (!isB2CUser) {
    // ignore: use_build_context_synchronously
    await disconnectAction(context: context);
  }
}

deleteUserAccountAction(
    {String? chassisNumber,
    String? code,
    BuildContext? context,
    String? imei}) async {
  http.Response response = await BackendApi.initiatePutCall(
    ApiUrls.markUserInActive,
  );
  await BackendApi.initiateGetCall(
    ApiUrls.authLogout,
  );

  if (response.statusCode != 200) {
    debugPrint("user account delete api failed");
  }
}
