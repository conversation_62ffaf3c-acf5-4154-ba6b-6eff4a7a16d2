import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/blocs/navigation/navigation_bar_stream.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_event.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/nearby_vehicle.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/companies/select_fleet_factory.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/streams/vehicle_status_data.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/alert/alert_message_with_close_button_action.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/widgets/dashboard/action_button_row.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../common/constant.dart';
import '../../common/shared_preferences_keys.dart';
import '../../constant/api_urls.dart';
import '../../constant/vehicle_status.dart';
import '../../services/api_service.dart';
import '../../widgets/dashboard/bottom_navigation.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  bool isNavigated = false;
  late Color color;
  late ColorType colorType;
  final NavigationBarStream navigationBarStream = NavigationBarStream();
  final VehicleStatusDataStream _vehicleStatusDataStream =
      VehicleStatusDataStream();

  @override
  void initState() {
    navigationBarStream.submitIndex(0);
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );
    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
    currentMarkers = {};
    currentNearbyPOI = NearbyPOI();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (isVehicleInfoAlertMessageExist == true &&
          currentVehicleStatus == VehicleStatus.connected) {
        getAlertMessageWithCloseButtonAction(context, alertMessages['text1']!);
      } else {
        isVehicleInfoAlertMessageExist == false;
        refreshVehicleInfo();
      }
    });
    super.initState();
  }

  action() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    currentContext = context;
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      resizeToAvoidBottomInset: false,
      appBar: PreferredSize(
          child: getAppBar(dimensions, action, context),
          preferredSize: Size.fromHeight(120 * dimensions.height)),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: StreamBuilder<VehicleStatus>(
          stream: _vehicleStatusDataStream.vehicleStatus,
          builder: (context, snapshot) {
            if (snapshot.data != null) {
              navigationBarStream.submitIndex(0);
              String hexColorInStr =
                  sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

              colorType = ColorType.values.firstWhere(
                (element) =>
                    element.toString() ==
                    sharedPreferences!.getString(vehicleThemeColorTypeKey),
                orElse: () => ColorType.normal,
              );

              color = hexColorInStr.isNotEmpty
                  ? hexColorInStr.toColor()
                  : colorGrey800;
              currentMarkers = {};
              currentNearbyPOI = NearbyPOI();
            }
            return getCustomBottomNavigationBar(
                context, action, color, colorType);
          }),
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) {
          if (state is InternetConnectivityFailure) {
            // Show the no internet connection modal here
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                // Once the bottom sheet is dismissed, reset the notifier
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (isBottomSheetOpenNotifier.value == true &&
              state is InternetConnectivitySuccess) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Navigator.of(context).pop();
              isBottomSheetOpenNotifier.value = false;
              SnackBarMessage.message(noInternetConnectionText["text5"]!,
                  backOnlineColorGreen, context);
            });
          }
        },
        child: SizedBox(
          height: 0.86 * dimensions.height,
          child: getBody(action, context, color, colorType),
        ),
      ),
    );
  }

  getAppBar(Dimensions dimensions, Function action, BuildContext buildContext) {
    return StreamBuilder<int>(
        stream: navigationBarStream.navigationBar,
        builder: (context, snapshot) {
          Widget widget = InkWell(
            splashFactory: NoSplash.splashFactory,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              OverlayEntry? entry =
                  buildContext.read<EditRiderDropDownBloc>().state.overlayEntry;
              if (entry != null) {
                entry.remove();
                entry = null;
                context
                    .read<EditRiderDropDownBloc>()
                    .add(const EditRiderDropDownEvent(overlayEntry: null));
              }
            },
            child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  border: const Border(
                    bottom: BorderSide(color: colorGrey400, width: 1.0),
                  ),
                ),
                width: dimensions.width,
                height: 120 / 896 * dimensions.height,
                child: ActionButtonRow(
                  isConnected: currentVehicleStatus == VehicleStatus.connected,
                  connectAction: () async {
                    if (currentVehicleStatus == VehicleStatus.disconnected) {
                      // ignore: use_build_context_synchronously
                      Navigator.of(context).push(
                        MaterialPageRoute(
                            builder: (context) =>
                                SelectFleetFactory.createSelectFleet()),
                      );
                    } else {
                      await DialogAction.disconnect.action(context: context);
                    }
                  },
                  context: context,
                  userName: userName,
                  isDashboardActionRow: true,
                )),
          );
          if (snapshot.data == 3) {
            widget = SizedBox(
              height: 40 / 896 * dimensions.height,
            );
          }

          return widget;
        });
  }

  refreshVehicleInfo() async {
    VehicleDataStream stream = VehicleDataStream();
    final pref = await SharedPreferences.getInstance();
    JsonDecoder decoder = const JsonDecoder();
    while (!isNavigated) {
      await Future.delayed(const Duration(seconds: 10));
      if (currentVehicleStatus == VehicleStatus.connected &&
          isVehicleInfoAlertMessageExist == false) {
        String imei = pref.getString(connectedVehicleImeiNo) ?? "";
        http.Response vehicleInfoResponse = await BackendApi.initiateGetCall(
            ApiUrls.vehicleInfo,
            params: {"imei": imei});
        Map<String, dynamic> vehicleResponse =
            decoder.convert(vehicleInfoResponse.body);
        if (vehicleInfoResponse.statusCode == 200 &&
            isVehicleInfoAlertMessageExist == false) {
          VehicleInfo vehicleInfo = VehicleInfo.fromJson(vehicleResponse);
          if (vehicleInfo.currentDriveMode?.toLowerCase() ==
              'null_drive_selection') {
            vehicleInfo.currentDriveMode = 'eco';
          }
          stream.updateVehicleInfo(vehicleInfo);
        } else if (currentVehicleStatus == VehicleStatus.connected) {
          isVehicleInfoAlertMessageExist = true;
          // ignore: use_build_context_synchronously
          getAlertMessageWithCloseButtonAction(
              // ignore: use_build_context_synchronously
              context,
              alertMessages['text1']!);
        }
      }
    }
  }

  @override
  void dispose() {
    isNavigated = true;
    super.dispose();
  }
}
