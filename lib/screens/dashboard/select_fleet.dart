import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/company/factoryFiles/connect_vehicle_factory.dart';
import 'package:nds_app/widgets/dashboard/action_button_row.dart';

class SelectFleet extends StatelessWidget {
  const SelectFleet({super.key});
  static const List<String> fleets = [companyName];

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    ThemeMode mode = MyApp.of(context).getCurrentThemeMode();
    return Scaffold(
      appBar: PreferredSize(
          preferredSize: Size.fromHeight(120 / 896 * dimensions.height),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              border: const Border(
                bottom: BorderSide(color: colorGrey400, width: 1.0),
              ),
            ),
            width: dimensions.width,
            height: 120 / 896 * dimensions.height,
            child: ActionButtonRow(
                isConnected: false,
                connectAction: () {},
                context: context,
                userName: "",
                isDashboardActionRow: false),
          )),
      body: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: 20 / 414 * dimensions.width,
            vertical: 24 / 896 * dimensions.height),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              selectFleetText["title"]!,
              style: Theme.of(context).textTheme.headlineLarge,
            ),
            SizedBox(
              height: 28 / 896 * dimensions.height,
            ),
            SizedBox(
                height: 610 / 896 * dimensions.height,
                child: getFleets(fleets, dimensions, context)),
            const Expanded(child: SizedBox()),
            Align(
              alignment: Alignment.bottomRight,
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            ConnectVehicleFactory.createConnectVehicle()),
                  );
                },
                child: Container(
                  height: 46 / 896 * dimensions.height,
                  width: 109 / 414 * dimensions.width,
                  alignment: Alignment.bottomRight,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.0),
                      border: Border.all(width: 1, color: colorGrey300),
                      color: Theme.of(context).canvasColor,
                      boxShadow: [
                        BoxShadow(
                            offset: const Offset(5, 5),
                            spreadRadius: 5,
                            blurRadius: 8,
                            color: mode == ThemeMode.light
                                ? colorGrey300.withOpacity(0.5)
                                : colorGrey600.withOpacity(0.5))
                      ]),
                  margin: const EdgeInsets.only(right: 4),
                  child: Center(
                    child: Text(
                      "Next",
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  GridView getFleets(
      List<String> fleets, Dimensions dimensions, BuildContext context) {
    return GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisSpacing: 16 / 414 * dimensions.width,
            mainAxisSpacing: 32 / 896 * dimensions.height,
            crossAxisCount: 2,
            childAspectRatio: (179 / 414 * dimensions.width) /
                (260 / 896 * dimensions.height)),
        itemCount: fleets.length,
        itemBuilder: (_, index) {
          return Container(
            width: 179 / 414 * dimensions.width,
            height: 260 / 896 * dimensions.height,
            padding: EdgeInsets.symmetric(
                horizontal: 24 / 414 * dimensions.width,
                vertical: 24 / 896 * dimensions.height),
            decoration: BoxDecoration(
                border: Border.all(color: selectedFleetBorderColor, width: 2),
                borderRadius:
                    BorderRadius.circular(16 / 414 * dimensions.width)),
            child: Column(
              children: [
                SizedBox(height: 20 / 896 * dimensions.height),
                SizedBox(
                  width: 80 / 414 * dimensions.width,
                  height: 80 / 896 * dimensions.height,
                  child: Image.asset(
                    clusterTitleRowCompanyLogo,
                    fit: BoxFit.fill,
                  ),
                ),
                SizedBox(height: 16 / 896 * dimensions.height),
                Text(
                  fleets[index],
                  style: Theme.of(context)
                      .textTheme
                      .headlineLarge
                      ?.copyWith(fontWeight: FontWeight.w700),
                ),
                Text(
                  company.afterConnectionCompanyLabel,
                  textAlign: TextAlign.center,
                  style: Theme.of(context)
                      .textTheme
                      .titleSmall
                      ?.copyWith(fontSize: 16 / 414 * dimensions.width),
                ),
              ],
            ),
          );
        });
  }
}
