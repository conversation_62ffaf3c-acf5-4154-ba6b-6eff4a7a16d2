import 'package:flutter/material.dart';
import 'package:nds_app/companies/home_view_factory.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

class HomeScreen extends StatefulWidget {
  final Function dashboardAction;
  const HomeScreen({
    super.key,
    required this.dashboardAction,
  });
  @override
  State<HomeScreen> createState() => HomeScreenState();
}

class HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_screen': 'Home Screen',
      'screen_class': widget.runtimeType.toString(),
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // Use the factory pattern to create the appropriate company-specific home view
    return HomeViewFactory.createHomeView(
      dashboardAction: widget.dashboardAction,
    );
  }
}
