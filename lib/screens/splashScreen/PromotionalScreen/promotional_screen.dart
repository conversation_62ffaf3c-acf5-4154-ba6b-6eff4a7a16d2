import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/companies/select_fleet_factory.dart';
import 'package:nds_app/screens/splashScreen/PromotionalScreen/promotional_place_holder.dart';
import 'package:nds_app/widgets/dashboard/action_button_row.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../blocs/connectivity/connectivity_bloc.dart';
import '../../../blocs/connectivity/connectivity_failure.dart';
import '../../../blocs/connectivity/connectivity_success.dart';
import '../../../blocs/connectivity/connectivty_state.dart';
import '../../../common/strings.dart';
import '../../../main.dart';
import '../../../utils/on_will_pop_popup.dart';
import '../../../utils/snake_bar.dart';
import '../../../widgets/common/offline_screen.dart';

class PromotionalScreen extends StatefulWidget {
  const PromotionalScreen({super.key});

  @override
  State<PromotionalScreen> createState() => _PromotionalScreenState();
}

class _PromotionalScreenState extends State<PromotionalScreen>
    with TickerProviderStateMixin {
  late Animation<double> _animation;
  late AnimationController _controller;

  List<String> images = [];

  @override
  void initState() {
    images = [];
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInCirc,
    );
    _controller.forward(
      from: 0,
    );

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return ColoredBox(
      color: Colors.white,
      child: Scaffold(
          appBar: PreferredSize(
              preferredSize: Size.fromHeight(120 * dimensions.height),
              child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    border: const Border(
                      bottom: BorderSide(color: colorGrey400, width: 1.0),
                    ),
                  ),
                  width: dimensions.width,
                  height: 120 / 896 * dimensions.height,
                  child: ActionButtonRow(
                    isConnected:
                        currentVehicleStatus == VehicleStatus.connected,
                    connectAction: () async {
                      if (currentVehicleStatus == VehicleStatus.disconnected) {
                        // ignore: use_build_context_synchronously
                        Navigator.of(context).push(
                          MaterialPageRoute(
                              builder: (context) =>
                                  SelectFleetFactory.createSelectFleet()),
                        );
                      } else {
                        await DialogAction.disconnect.action(context: context);
                      }
                    },
                    context: context,
                    userName: userName,
                    isDashboardActionRow: true,
                  ))),
          body:
              BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
            listener: (context, state) {
              if (state is InternetConnectivityFailure) {
                // Show the no internet connection modal here
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  getBotttomNoInternetConnection(
                    heading: noInternetConnectionText["text4"]!,
                    context: context,
                  ).then((_) {
                    // Once the bottom sheet is dismissed, reset the notifier
                    isBottomSheetOpenNotifier.value = false;
                  });
                });
              } else if (isBottomSheetOpenNotifier.value == true &&
                  state is InternetConnectivitySuccess) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Navigator.of(context).pop();
                  isBottomSheetOpenNotifier.value = false;
                  SnackBarMessage.message(noInternetConnectionText["text5"]!,
                      backOnlineColorGreen, context);
                });
              }
            },
            child: Stack(
              children: [
                Column(
                  children: [
                    const Expanded(child: PromotionalPlaceHolder()),
                    SizedBox(
                      height: 32 / 896 * dimensions.height,
                    ),
                    Column(
                      children: [
                        SizedBox(
                          height: 48 / 896 * dimensions.height,
                          child: CustomButton.elevated(
                            width: 300 / 414 * dimensions.width,
                            backgroundColor: colorError600,
                            onPressed: () {
                              Uri url = Uri.parse(website);
                              launchUrl(url);
                            },
                            text: "Go To Website",
                            foregroundColor: colorWhite,
                            fontSize: 20 / 414 * dimensions.width,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(
                          height: 20 / 896 * dimensions.height,
                        ),
                        SizedBox(
                          height: 48 / 896 * dimensions.height,
                          child: CustomButton.elevated(
                            width: 300 / 414 * dimensions.width,
                            backgroundColor: colorGrey400,
                            onPressed: () {
                              onWillPopUpMsg(
                                context,
                                DialogAction.logout,
                              );
                            },
                            text: profileScreen['menu5']!,
                            foregroundColor: colorWhite,
                            fontSize: 20 / 414 * dimensions.width,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(
                          height: 32 / 896 * dimensions.height,
                        ),
                        GestureDetector(
                          onTap: () async {
                            Uri url = Uri.parse(website);
                            launchUrl(url);
                          },
                          child: Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                AnimatedContainer(
                                  alignment: Alignment.bottomCenter,
                                  padding: EdgeInsets.only(
                                      bottom: 20 / 896 * dimensions.height),
                                  duration: const Duration(seconds: 1),
                                  curve: Curves.easeInOut,
                                  width: (0.20) * dimensions.width,
                                  child: Image.asset(
                                    splashScreenLoadingScreenCompanyLogo2,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                                FadeTransition(
                                    opacity: _animation,
                                    child: AnimatedContainer(
                                      alignment: Alignment.bottomCenter,
                                      duration: const Duration(seconds: 1),
                                      curve: Curves.easeInOut,
                                      padding: EdgeInsets.only(
                                          bottom: 28 / 896 * dimensions.height),
                                      width: (0.25) * dimensions.width,
                                      child: Image.asset(
                                        splashScreenLoadingScreenCompanyLogo3,
                                        fit: BoxFit.contain,
                                      ),
                                    ))
                              ]),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 32 / 896 * dimensions.height,
                    ),
                  ],
                ),
              ],
            ),
          )),
    );
  }
}
