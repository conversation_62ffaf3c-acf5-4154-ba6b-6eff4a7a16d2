const Map<String, String> homeScreenText = {
  "user_name": "Hello @value,",
  "text1": "Total Distance",
  "text2": "Vehicles Available",
  "text3": "TAP TO SCAN",
  "text4": "Scan your vehicle to start the ride",
  "text5": "Fuel Savings",
  "text6": "Estimated Values",
  "text8": "Saved",
  "text9": "Total Spent",
  "text10": "Fuel Equivalent",
  "text11": "@value%",
  "text12": "NDS Motors",
  "text13": "Battery is Connected..",
  "text14": "LML",
  "text15": "NDS",
  "text16": "NICHESOLV",
  "text17": "SIMPSON",
  "battery_removed": "Battery removed",
  "battery_low": "Battery level is low 5% Please charge",
  "battery_temperature": "The battery temperature is too high",
  "alert_time": "1h ago",
  "connected_label": "Connected",
  "text18": "In @range km",
  "text19": "LAPA",
  "text20": "@value",
  "text21": "Battery Removed",
  "text22": "Vehicle Charging",
  "text23": "My Activities",
  "text24": "Ride",
  "text25": "Time(h)",
  "text26": "Distance(kms)",
  "text27": "Total Test",
  "text28": "tests recorded",
  "text29": "PRODRED",
};
const Map<String, String> b2cHomeScreenText = {
  "popup_title": "Scooter Connect",
  "text1": "Access",
  "text2": "owner",
  "button1": "Connect",
  "button2": "Connected",
  "button3": "Accept",
  "button4": "Reject",
  "button5": "Disconnect",
  "button6": "Close",
};

const Map<String, String> commonText = {
  "text1": "Connect",
  "text2": "Disconnect",
  "emptyImeiMsg": "please select vehicle",
  "labelCancel": "Cancel",
  "appTitle": "NDS App",
  "labelYes": "Yes",
  "labelNo": "No",
  "button1": "Back",
  "EditTitle": "Edit",
  "EditLabel1": "Delete",
};
const Map<String, String> clusterScreenText = {
  "lml": "LML",
  "temp": "temp @temp @unit",
  "volts": "@volts V",
  "kmUnit": "km/h",
  "odo": "ODO @km km",
  "km": " @km km ",
  "range": "Estimated Range",
  "amps": "AMPS",
};
const Map<String, String> loginScreen = {
  "text1": "Welcome! Login to \nget started!",
  "text2": "Mobile Number",
  "text3": "Enter your mobile number",
  "text4": "Send OTP",
  "text6": "Enter a valid mobile number",
  "text7": "I have read and agree to the ",
  "text8": "Privacy Policy",
  "text9": " and ",
  "text10": "Terms and Conditions",
  "text11": ".",
  "text12": "New Privacy Policy",
  "text13": "New Terms and Conditions",
  "text14": "I agree",
  "text15": "Enter your Mobile Number",
  "text16": "We will send you the 6 digit verification code",
};
const Map<String, String> otpVerificationScreen = {
  "text1": "OTP Verification",
  "text2": "Enter the verification code we just sent on your mobile number",
  "text3": "Didn't receive OTP?",
  "text4": "Resend",
  "text5": "Verify",
  "text6": "The OTP entered is incorrect.",
  "text7": "Verify your mobile number",
  "text8": "We sent a 6-digit code to +91 ",
  "text9": "Resend code in @value s ",
};

const Map<String, String> profileScreen = {
  "title1": "Profile",
  "title2": "Profile Details",
  "title3": "Edit Profile",
  "menu1": "Profile Details",
  "menu2": "Documents",
  "menu3": "Settings",
  "menu4": "Help",
  "menu5": "Logout",
  "menu6": "About Vehicle",
  "menu7": "Scooter Access",
  "menu8": "Delete Account",
  "titleFirstName": "First Name",
  "titleLastName": "Last Name",
  "detail1": "Name",
  "detail2": "DOB",
  "detail3": "Gender",
  "detail4": "Email",
  "detail5": "Mobile Number",
  "detail6": "Aadhar Number",
  "detail7": "Address",
  "detail8": "Weight(kg)",
  "detail9": "Blood Group",
  "detail10": "Emergency Contact Number",
  "update": "Update",
  "button": "Save Changes",
  "hint1": "Enter first name",
  "hint2": "Enter last name",
  "hint3": "Enter date of birth",
  "hint4": "Select a gender",
  "hint5": "Enter email address",
  "hint6": "Enter mobile number",
  "hint8": "Enter address",
  "hint9": "Enter Emergency Number",
  "hint10": "Select your Blood Group",
  "hint11": "Select your weight",
  "male": "Male",
  "female": "Female",
  "error": "Error while updating profile details!",
  "success_msg": "Successfully uploaded profile picture!",
  "update_success": "Profile updated successfully!",
  "error_msg": "Error while uploading profile picture!",
  "image_size_error_msg": "Please select an image smaller than 1 MB",
  "contact1": "Our 24x7 Customer Service",
  "contact2": "Write us at",
  "": "Vehicle Visibilty",
  "setting1": "Vehicle Visibility",
  "setting2": "Home Display Name",
  "weight_in_kg": "@weight kg",
  "weight_unit": "Kg",
  "aboutVehicle1": "Reg No.",
  "aboutVehicle2": "Manufacturer",
  "aboutVehicle3": "Range",
  "aboutVehicle4": "Net Weight",
  "aboutVehicle5": "Vehicle Color",
  "aboutVehicle6": "Vehicle Specifications",
  "aboutVehicle7": "Height",
  "aboutVehicle8": "Width",
  "aboutVehicle9": "Battery & Charger",
  "aboutVehicle10": "Battery Name",
  "aboutVehicle11": "Battery Manufacturer",
  "aboutVehicle12": "Battery Capacity",
  "aboutVehicle13": "Tyre",
  "aboutVehicle14": "Rear Tyre Diameter",
  "aboutVehicle15": "Front Tyre Diameter",
  "aboutVehicle16": "Tyre Manufacturer",
  "aboutVehicle17": "___",
  "aboutVehicleValue1": "@value1 - @value2 km/charge",
  "aboutVehicleValue2": "@value kg",
  "aboutVehicleValue3": "@value Ah",
  "aboutVehicleValue4": "@value meter",
  "save_button": "Save",
  "scooterAccess1": "No riders added yet",
  "scooterAccess2":
      "Tap below to share access to your scooter with other riders",
  "scooterAccess3": "Authorized User List",
  "scooterAccess4": "Verification Pending",
  "scooterAccess5": "Link sent",
  "scooterAccessBottomSheet1": "Add a rider",
  "scooterAccessBottomSheet2": "How would you like to add your rider details ?",
  "scooterAccessBottomSheet3": "Add rider manually",
  "scooterAccessBottomSheet4": "Choose from contacts",
  "scooterAccessRiderDetailsTitle": "Rider Details",
  "scooterAccessRiderDetailsEditTitle": "Edit Rider Details",
  "scooterAccessRiderDetails1": "Name",
  "scooterAccessRiderHint1": "Enter Name",
  "scooterAccessRiderDetails2": "Mobile Number",
  "scooterAccessRiderHint2": "Enter Mobile Number",
  "scooterAccessRiderDetails3": "Relation",
  "scooterAccessRiderDetails4": "Family",
  "scooterAccessRiderDetails5": "Friend",
  "scooterAccessRiderDetails6": "Relatives",
  "scooterAccessRiderDetails7": "Others",
  "scooterAccessRiderDetails8": "Save",
  "scooterAccessRiderDetails9": "Share",
  "scooterAccessRiderDetails10": "Updated",
  "scooterAccessRiderDetails11": "Added",
  "scooterAccessRiderDetails12": "Shared",
  "scooterAccessRiderDetails13": "Add",
  "scooterAccessRiderDetails14": "Rider Added",
  "scooterAccessRiderDetails15": "Go Back",
  "scooterAccessRiderDetails16": "Rider Updated",
  "scooterAccessRiderMessage1":
      "phone number is already associated with vehicle",
  "scooterAccessRiderMobileAlert":
      "Please enter a valid 10-digit mobile number",
  "rejected": "Rejected"
};

const List<int> vehicleVisibityRangeOptions = [10, 20, 30, 40, 50];

const List<String> homeDisplayNameOptions = ["Username", "Registration Name"];

const List<String> bloodGroupOptions = [
  "B+",
  "B-",
  "O+",
  "O-",
  "A+",
  "A-",
  "AB-",
  "AB+",
];

const Map<String, String> vehicleScreen = {
  "title1": "Your vehicles",
  "title2": "Vehicle Details",
  "search": "Search for Vehicle name, number",
  "connected": "Connected",
  "last_connected": "Last connected",
  "label_charge": "Charge On Last Disconnect",
  "label_distance": "Distance covered",
  "expired": "Expired",
  "available": "Available",
  "certificate1": "Registration Certificate",
  "certificate2": "Pollution Certificate",
  "certificate3": "Insurance Certificate",
  "noVehicles": "No results found",
  "toggleLeft": "Your vehicles",
  "toggleRight": "Trip History",
  "vehicleHealth": "Vehicle Health",
  "vehicleHealthStatus": "Good",
  "vehicleHealthInfo": "Health Info",
  "status": "Status",
  "noHealthData":
      "Vehicle health data is currently unavailable. Please try again later.",
  "noPartData": "Part health is currently not available for the selected part.",
};

const Map<String, String> selectFleetText = {
  "title": "Select You're Fleet",
  "button1": "Next",
};

const Map<String, String> connectVehicleText = {
  "text1": "Enter Code",
  "text2": "Enter last four digits of Chassis Number or IMEI",
  "error1": "No Permission",
  "text3": "QR SCAN",
  "text4": "Code",
  "text5": "To get started",
  "text6": "To get started \n Scan the QR on the vehicle",
  "error2": "Invalid QR Code: Please scan a valid QR code.",
};

const Map<String, String> toastMessageText = {
  "text1": "vehicle @code is connected",
  "text2": "vehicle @code details not found",
  "text3": "vehicle @code is disconnected",
  "text4": "Privacy policy retrieval failed. please try again later",
  "text5": "Term and Conditions retrieval failed. please try again later",
  "text6": "No Internet Connection",
  "insight1": "Term and Conditions retrieval failed. please try again later",
  "connectRiderButton1": "Please accept the invitation to proceed",
  "connectRiderButton2": "Please Select the Vehicle",
  "connectRiderButton3": "permission revoked for this vehicle",
  "connectRiderButton4": "can't disconnect  own vehicle",
  "connectRiderButton5": "You currently don't have any owned vehicle",
  "connectRiderButton6": "please disconnect vehicle to select",
  "connectRiderButton7": "Owner can't be disconnected",
  "connectRiderButton8":
      "Currently you don't own a vehicle and don't have an invitation to connect",
  "connectRiderButton9": "Owner can't add his own number",
  "connectRiderButton10": "Rider name can't be empty",
};

const Map<String, String> alertMessages = {
  "text1": "Vehicle info failed to load. Please try again later",
};
const Map<String, String> insightsText = {
  "text1": "Trip History",
  "text2": "This Week",
  "no_data": "No trip found",
  "text3": "Distance Travelled",
  "text4": "Ride Duration",
  "text5": "Average Speed",
  "text6": "Top Speed",
  "text7": "Charging Time",
  "text8": "Number of Swaps",
  "text9": "Average Range",
  "text10": "km",
  "text11": "hr",
  "text12": "min",
  "text13": "km/hr",
  "text14": "per week",
  "text15": "Mix ",
  "text16": "Ride Details",
  "text17": "Fuel Savings",
  "text18": "EV vs Fuel: Comparison",
  "text19": "Fuel(Petrol)",
  "text20": "EV",
  "text21": "FUEL COST",
  "text22": "Currently No data Available to Compare",
  "text23": "Trips",
  "text24": "Trips Tracker",
  "text25": "Total Distance",
  "text26": "Total Hours",
  "text27": "Ride Count",
  "text28": "Please connect to a vehicle to view or add activity.",
  "text29": "Track As Test Ride",
  "text30": "You can track a trip as test ride for detailed analysis.",
  "text31":
      "Use this section to add metrics like battery temperature or voltage. It could be pre-trip, post-trip or during the trip.",
  "text32":
      "Use this section to add descriptive notes about the trip like road type or climate.",
  "text33": "Start Time",
  "text34": "End Time",
  "text35": "Trip",
  "text36": "kms",
  "text37": "Add",
  "text38": "View",
  "text39": "Ride activity added successfully",
  "text40": "Enter Notes...",
  "text41": "Ride submitted successfully!",
  "text42": "Ride validation aborted try again later",
  "text43": "Ride validation in progress",
  "text44": "Error validating the Ride try again later",
  "text45":
      "This section displays the distance covered in manual trips using this vehicle.",
  "text46": "sec",
  "text47":
      "Trip completion in progress. This may take up to 10 minutes. Please wait.",
  "text48": "No more trips to load"
};

const Map<String, String> rideActivityText = {
  "text1": "No start time available",
  "text2": "No end time available",
  "text3": "None",
  "text4": "Trip start time is missing",
  "text5": "Trip end time is missing",
  "text6": "No start time available",
  "text7": "select start time",
  "text8": "Please select a start time",
  "text9": "No end time available",
  "text10": "select end time",
  "text11": "Please select an end time",
  "text12": "End time cannot be before start time",
  "text13": "Please enter rider weight",
  "text14": "Rider weight must be greater than 0 kg",
  "text15": "Rider weight must be at least 10kg",
  "text16": "Please enter a valid rider weight",
  "text17": "Vehicle IMEI not found. Please connect to a vehicle first.",
  "text18": "Failed to submit Ride Activity try again later",
  "text19": "Error",
  "text20": "Trip",
  "text21": "Rider Weight",
  "text22": "Pillion Weight",
  "text23": "Submit",
  "text24": "kgs",
  "text25": "Required",
  "text26": "cancel",
  "text27": "Weight must be greater than 0kg",
  "text28": "Weight must be at least 10kg",
  "text29": "Please enter a valid weight",
  "text30": "Please enter a valid rider weight",
  "text31": "Ok",
  "text32": "Metrics",
  "text33": "Metrics Name",
  "text34": "value",
  "text35": "Observations",
  "text36": "Enter Your Notes...",
};

const Map<String, String> alertTempMessages = {
  "text1": "Battery Temperature",
  "text2": "Please",
  "text3": " Stop The Vehicle",
  "text4": " and",
  "text5":
      " and MOVE AWAY to a safe distance immediately. Wait until the Battery cools down to @value°C",
  "text6": "Normal @value",
  "text7": "Now You Can Start The Vehicle",
  "text8": "OK",
};

const Map<String, String> noInternetConnectionText = {
  "text1": "No Internet connection",
  "text2":
      "Something went wrong Try refreshing the page or checking your internet connection We’ll see you in a moment !",
  "text3": "Try again",
  "text4": "You're Offline",
  "text5": "Back Online",
};

const Map<String, String> checkUpdateScreenText = {
  "text1": "New Update Available!",
  "text2": "Please update to the latest version of the app.",
  "text3": "Update",
  "text4":
      "We've got exciting enhancements and bug fixes in this latest version.",
  "text5": "Update now to enjoy a smoother and better experience.",
  "text6": "Tap 'Update' to get started.",
  "text7": "• ",
};

const Map<String, String> rideDetailsScreenText = {
  "text1": 'Ride Details',
  "text2": "Overview",
  "text3": "Routes",
  "text4": "Test Trip",
  "text5": "Speed",
  "text6": "Charge",
  "text7": "Speed Analysis",
  "text8": "Distance",
  "text9": "Duration",
  "text10": "Avg",
  "text11": "Max",
  "text12": "Starting",
  "text13": "Ending",
  "text14": "Discharge",
  "text15": "ECO",
  "text16": "CITY",
  "text17": "POWER",
  "text18": "Frequent Speed",
  "text19": "Playback Speed",
  "text20": "kms",
  "text21": "km/hr",
};
const Map<String, String> onBoardingScreenText = {
  "title1": "Monitor Battery and Range",
  "description1":
      "Track battery life and remaining mileage to ensure a worry-free journey.",
  "title2": "Analyze Your Performance",
  "description2":
      "View detailed insights on your driving habits, trip history, and fuel savings.",
  "skip_button": "Skip",
  "next_button": "Next",
  "get_started_button": "Get Started",
};

const Map<String, String> splashScreenText = {
  "loading_message": "Loading...",
  "initializing": "Initializing application",
  "please_wait": "Please wait",
};

const Map<String, String> loadingScreenText = {
  "loading_message": "Loading your experience",
  "preparing_app": "Preparing your app",
  "almost_ready": "Almost ready",
  "please_wait": "Please wait while we set things up",
};

const Map<String, String> promotionalScreenText = {
  "go_to_website": "Go To Website",
  "logout_button": "Logout",
  "welcome_message": "Welcome to our platform",
  "explore_features": "Explore amazing features",
  "get_started": "Get Started",
};
