import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/screens/splashScreen/splash_screen.dart';
import 'package:nds_app/services/firebase_remote_config_serivce.dart';
import 'package:nds_app/services/package_info_setup.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:open_store/open_store.dart';

class CheckAppUpdateScreen extends StatefulWidget {
  const CheckAppUpdateScreen({super.key});

  @override
  State<CheckAppUpdateScreen> createState() => _CheckAppUpdateScreenState();
}

class _CheckAppUpdateScreenState extends State<CheckAppUpdateScreen> {
  final packageInfo = PackageInfoSetup();
  final firebaseRemoteConfigService = FirebaseRemoteConfigService();
  bool _showUpdateDialog = false;
  @override
  void initState() {
    setup();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_showUpdateDialog) {
        _showUpdateVersionDialog(context);
      } else {
        _navigateToNextPage();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) {
          if (state is InternetConnectivityFailure) {
            // Show the no internet connection modal here
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                // Once the bottom sheet is dismissed, reset the notifier
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (isBottomSheetOpenNotifier.value == true &&
              state is InternetConnectivitySuccess) {
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              Navigator.of(context).pop();
              isBottomSheetOpenNotifier.value = false;
              SnackBarMessage.message(noInternetConnectionText["text5"]!,
                  backOnlineColorGreen, context);
              await Navigator.pushReplacement(
                  // ignore: use_build_context_synchronously
                  context,
                  MaterialPageRoute(
                      builder: (context) => const CheckAppUpdateScreen()));
            });
          }
        },
        child: BlocBuilder<InternetConnectivityBloc, InternetConnectivityState>(
          builder: (context, state) {
            return Container();
          },
        ),
      ),
    );
  }

  int _getExtendedVersionNumber(String version) {
    List versionCells = version.split('.');
    versionCells = versionCells.map((i) => int.parse(i)).toList();
    return versionCells[0] * 100000 + versionCells[1] * 1000 + versionCells[2];
  }

  Future<void> _showUpdateVersionDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        Dimensions dimensions = Dimensions(context);
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 62 / 896 * dimensions.height,
                  width: 62 / 414 * dimensions.width,
                  child: Image.asset(splashScreenLoadingPageCompanyLogo1),
                ),
                SizedBox(
                  height: 16 / 896 * dimensions.height,
                ),
                Text(
                  packageInfo.appName,
                  style: poppinsTextStyle(20 / 414 * dimensions.width,
                      colorGrey800, FontWeight.w600),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: ListBody(
                children: <Widget>[
                  Text(
                    checkUpdateScreenText['text1']!,
                    style: poppinsTextStyle(16 / 414 * dimensions.width,
                        colorGrey600, FontWeight.w500),
                  ),
                  SizedBox(
                    height: 4 / 896 * dimensions.height,
                  ),
                  ...getBulletPointSteps(dimensions),
                ],
              ),
            ),
            actions: <Widget>[
              Center(
                child: SizedBox(
                  width: 100 / 414 * dimensions.width,
                  child: CustomButton.text(
                    backgroundColor: loginThemeColor,
                    borderRadius: 40 / 414 * dimensions.width,
                    text: checkUpdateScreenText['text3']!,
                    foregroundColor: colorWhite,
                    fontSize: 16 / 414 * dimensions.width,
                    fontWeight: FontWeight.w400,
                    onPressed: () {
                      _launchAppOrPlayStore();
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future setup() async {
    final appVersion = _getExtendedVersionNumber(packageInfo.version);
    final requiredMinVersion = _getExtendedVersionNumber(
        firebaseRemoteConfigService.getMinRequiredVersion());
    debugPrint("------requiredMinVersion : $requiredMinVersion");
    if (appVersion < requiredMinVersion) {
      _showUpdateDialog = true;
    }
  }

  void _launchAppOrPlayStore() {
    OpenStore.instance.open(
      appStoreId: iosAppId,
      androidAppBundleId: packageInfo.packageName,
    );
  }

  void _navigateToNextPage() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const LoadingPage(),
      ),
    );
  }

  List getBulletPointSteps(Dimensions dimensions) {
    List<String> texts = [
      checkUpdateScreenText['text4']!,
      checkUpdateScreenText['text5']!,
      checkUpdateScreenText['text6']!,
    ];
    List<Widget> steps = [];
    for (String text in texts) {
      steps.add(Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            checkUpdateScreenText['text7']!,
            style: poppinsTextStyle(
                12 / 414 * dimensions.width, colorGrey400, FontWeight.w400),
          ),
          SizedBox(
            width: 244 / 414 * dimensions.width,
            child: Text(
              text,
              overflow: TextOverflow.fade,
              style: poppinsTextStyle(
                  12 / 414 * dimensions.width, colorGrey400, FontWeight.w400),
            ),
          ),
        ],
      ));
      steps.add(SizedBox(
        height: 4 / 896 * dimensions.height,
      ));
    }

    return steps;
  }
}
