import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/models/enums/alert_type.dart';
import 'package:nds_app/models/nearby_vehicle.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/models/user_info.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../branding/branding.dart';

VehicleStatus currentVehicleStatus = VehicleStatus.disconnected;

VehicleInfo? vehicleInfoConstant;
UserInfo? userInfo;
String userName = "";
bool isDetailPage = false;
Vehicle? vehicle;
SharedPreferences? sharedPreferences;
bool isVehicleInfoAlertMessageExist = false;
const bool isB2CUser =
    String.fromEnvironment('company', defaultValue: "nds") == "b2c" ||
        String.fromEnvironment('company', defaultValue: "nds") == "lapa";
const bool isLapaUser =
    String.fromEnvironment('company', defaultValue: "nds") == "lapa";
const bool isProdRedUser =
    String.fromEnvironment('company', defaultValue: "nds") == "prodred";
const bool isNDSUser =
    String.fromEnvironment('company', defaultValue: "nds") == "nds";
const bool isNichesolvUser =
    String.fromEnvironment('company', defaultValue: "nds") == "nichesolv";
const String env = String.fromEnvironment('env', defaultValue: "dev");
LocationData? userLocationData;
bool isGPSModeEnabled = true;
String defaultImageTag = 'MAIN';
bool isTempAlertMessageExist = false;
TempAlertType currentTempAlertType = TempAlertType.none;
BuildContext? currentContext;
double? currentMaxTemp = 0;
bool isExistNoInternetConnectionScreen = false;
bool? isInternetAvailable;
NearbyPOI currentNearbyPOI = NearbyPOI();
Set<Marker> currentMarkers = {};
bool requestPermissionPopExist = false;
UserActivitySetting? privacyPolicySetting;
UserActivitySetting? termsAndConditionsSetting;
bool isVehicleToggleSwitched = false;
bool isTwoWheels = noOfWheels == 2;
const String nichesolvContactPhoneNumber = "+91 70229 92173";
const String nichesolvContactMail = "<EMAIL>";

const String ndsContactPhoneNumber = "+91 8431084965";
const String ndsContactMail = "<EMAIL>";

const String lmlContactPhoneNumber = nichesolvContactPhoneNumber;
const String lmlContactMail = nichesolvContactMail;

const String simpsonContactPhoneNumber = nichesolvContactPhoneNumber;
const String simpsonContactMail = nichesolvContactMail;

const String lapaContactPhoneNumber = "+91 9480886072";
const String lapaContactMail = "<EMAIL>";

const int nichesolvOranisationId = 2;
const String nichesolvOtpSenderId = "CP-NchEV";
const int organisationId =
    int.fromEnvironment('org_id', defaultValue: nichesolvOranisationId);
int bottomNavigationIndex = 0;
const ndsWebsite = "https://www.ndsecomotors.com/";
const nichesolvWebsite = "https://www.Nichesolv.com/";
const lapaWebsite = "https://lapaelectric.com/";

const String ndsB2BIosAppId = "6737301460";
const String ndsB2BAndroidPackageName = "www.ndsecomotors.com";

const String ndsB2CIosAppId = "6737301655";
const String ndsB2CAndroidPackageName = "www.ndsecomotors.com.b2c";

const String lapaB2CIosAppId = "";
const String lapaB2CAndroidPackageName = "";

const String nichesolvB2BIosAppId = "";
const String nichesolvB2BAndroidPackageName = "";

const String lmlB2BIosAppId = "";
const String lmlB2BAndroidPackageName = "";

const String simpsonB2BIosAppId = "";
const String simpsonB2BAndroidPackageName = "";

const int ndsWheels = 2;
const int nichesolvWheels = 2;
const int lapaWheels = 2;
const int simpsonWheels = 2;
const int imlWheels = 2;
const int prodRedWheels = 2;

const Map<String, String> trackingLabels = {
  "ConnectAction": "vehicle_connect_action",
  "DisconnectAction": "vehicle_disconnect_action",
  "ClusterButtonAction": "cluster_button_action",
  "ScrollHomeScreenAction": "scroll_home_screen_action",
  "ZoomMapAction": "zoom_map_action",
  "LoginAction": "login_action",
  "LogoutAction": "logout_action",
};

String sharedRiderDetailsMessageFormat = '''
Hi! I’m @ownerName, owner of vehicle @vehicleRegNo.
You can now access the vehicle through the app! Log in with your contact number: @riderPhoneNumber.
Get the App and login:
Android: https://play.google.com/store/apps/details?id=@androidPackageName
iOS: https://apps.apple.com/us/app/id@iosAppId
Enjoy the ride!
    ''';

void resetVariables() {
  currentVehicleStatus = VehicleStatus.disconnected;
  vehicleInfoConstant = null;
  userInfo = null;
  userName = "";
  isDetailPage = false;
  vehicle = null;
  sharedPreferences = null;
  isVehicleInfoAlertMessageExist = false;
  userLocationData = null;
  isGPSModeEnabled = true;
  isTempAlertMessageExist = false;
  currentTempAlertType = TempAlertType.none;
  currentContext = null;
  currentMaxTemp = 0;
  isExistNoInternetConnectionScreen = false;
  isInternetAvailable = null;
  currentNearbyPOI = NearbyPOI();
  currentMarkers = {};
  requestPermissionPopExist = false;
  bottomNavigationIndex = 0;
}
