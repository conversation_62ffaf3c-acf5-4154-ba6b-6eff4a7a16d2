import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_blocs.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_state.dart';
import 'package:nds_app/blocs/insight/toggle/insight_toggle_bloc.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/enums/statistics_data_type.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/widgets/insights/fuel_savings_screen.dart';
import 'package:nds_app/widgets/insights/ride_details_fuel_saving_toggle_button.dart';
import 'package:nds_app/widgets/insights/statistics_screen.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

import '../../../../common/dimensions.dart';

import '../../baseScreens/insights/base_insights.dart';

/// Template One for insights screens (ProdRed style - Statistics only)
/// Contains insights functionality with ProdRed-specific UI behavior
/// Used by company that need simplified insights view
class InsightsTemplateOneScreen extends BaseInsights {
  const InsightsTemplateOneScreen({
    super.key,
    required super.color,
    required super.colorType,
  });

  @override
  State<InsightsTemplateOneScreen> createState() =>
      _InsightsTemplateOneScreenState();
}

class _InsightsTemplateOneScreenState extends State<InsightsTemplateOneScreen> {
  late Color color;
  late ColorType colorType;
  @override
  void initState() {
    color = widget.color;
    colorType = widget.colorType;
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Insights Template One',
      'screen_class': widget.runtimeType.toString(),
    });
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );

    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Column(
      children: [
        isProdRedUser
            ? StatisticsScreen(color: color, colorType: colorType)
            : Expanded(
                child: Column(
                  children: [
                    BlocBuilder<StatisticsBloc, StatisticsState>(
                      builder: (context, state) {
                        return Visibility(
                          visible: state.statisticsDataType ==
                              StatisticsDataType.statistics,
                          child: Column(
                            children: [
                              SizedBox(
                                height: 22 / 896 * dimensions.height,
                              ),
                              ToggleButtonRideDetailsAndFuelSaving(
                                  color: color, colorType: colorType),
                              SizedBox(
                                height: 12 / 896 * dimensions.height,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    SizedBox(
                      height: 8 / 896 * dimensions.height,
                    ),
                    BlocBuilder<InsightToggleBloc, InsightToggleState>(
                        builder: (context, state) {
                      return state.isSwitchRight
                          ? const Expanded(child: FuelSavingsScreen())
                          : StatisticsScreen(
                              color: color, colorType: colorType);
                    }),
                  ],
                ),
              ),
      ],
    );
  }
}
