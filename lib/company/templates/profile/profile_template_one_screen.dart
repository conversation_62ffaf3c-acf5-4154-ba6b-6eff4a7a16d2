import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/companies/factoryFiles/about_vehicle_factory.dart';
import 'package:nds_app/companies/factoryFiles/help_factory.dart';
import 'package:nds_app/companies/factoryFiles/profile_home_factory.dart';
import 'package:nds_app/screens/vehicle/vehicleHealth/vehicle_health_detail_page.dart';
import 'package:nds_app/screens/vehicle/vehicleHealth/vehicle_health_page.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../factoryFiles/edit_profile_factory.dart';
import '../../factoryFiles/profile_details_factory.dart';
import '../../factoryFiles/setting_factory.dart';
import '../../baseScreens/profile/base_profile.dart';

/// Template for profile screens
/// Contains all the profile functionality and UI
/// Used by all company that share the same profile template
class ProfileTemplateOneScreen extends BaseProfile {
  const ProfileTemplateOneScreen({super.key});

  @override
  State<ProfileTemplateOneScreen> createState() =>
      _ProfileTemplateOneScreenState();
}

class _ProfileTemplateOneScreenState extends State<ProfileTemplateOneScreen> {
  late Widget activeScreen;
  String imei = "";
  Vehicle? vehicle;

  @override
  void initState() {
    super.initState();
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': "Profile Template One",
      'screen_class': widget.runtimeType.toString(),
    });

    activeScreen = ProfileHomeFactory.createProfileHome(
      openDetails: openProfileDetails,
      openHelp: openHelpPage,
      openSetting: openSetting,
      openAboutVehicle: openAboutVehiclePage,
    );
    _loadVehicleInfo();
  }

  Future<void> _loadVehicleInfo() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    imei = pref.getString(connectedVehicleImeiNo) ?? "";
    // Create a basic Vehicle object with the required fields
    if (imei.isNotEmpty) {
      setState(() {
        vehicle = Vehicle(
          regNo: imei,
          model: pref.getString('vehicle_model') ?? "My Vehicle",
          images: [],
          soc: 0,
          distanceCovered: 0,
          startDate: DateTime.now().millisecondsSinceEpoch,
        );
      });
    }
  }

  void openProfileDetails() {
    setState(() {
      activeScreen = ProfileDetailsFactory.createProfileDetails(
        editProfile: openEditProfile,
        onBackPressed: openProfileHome,
      );
    });
  }

  void openEditProfile() {
    setState(() {
      activeScreen = EditProfileFactory.createEditProfile(
        onBackPressed: openProfileDetails,
      );
    });
  }

  void openHelpPage() {
    setState(() {
      activeScreen = HelpFactory.createHelp(
        onBackPressed: openProfileHome,
      );
    });
  }

  void openSetting() {
    setState(() {
      activeScreen = SettingFactory.createSetting(
        onBackPressed: openProfileHome,
      );
    });
  }

  void openAboutVehiclePage() {
    setState(() {
      activeScreen = AboutVehicleFactory.createAboutVehicle(
        onBackPressed: openProfileHome,
        healthPressed: vehicleHealthPressed,
      );
    });
  }

  void vehicleHealthPressed() {
    if (vehicle != null) {
      setState(() {
        activeScreen = VehicleHealthPage(
          imei: vehicle!.regNo,
          onBackPressed: vehicleHealthOnBackPressed,
          onHealthDetailPressed: vehicleHealthDetailPressed,
          vehicle: vehicle!,
        );
      });
    }
  }

  void vehicleHealthOnBackPressed() {
    setState(() {
      activeScreen = AboutVehicleFactory.createAboutVehicle(
        onBackPressed: openProfileHome,
        healthPressed: vehicleHealthPressed,
      );
    });
  }

  void vehicleHealthDetailPressed(String partType, String partLabel) {
    if (vehicle != null) {
      setState(() {
        activeScreen = VehicleHealthDetailPage(
          onBackPressed: vehicleHealthDetailOnBackPressed,
          imei: vehicle!.regNo,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle!,
        );
      });
    }
  }

  void vehicleHealthDetailOnBackPressed() {
    if (vehicle != null) {
      setState(() {
        activeScreen = VehicleHealthPage(
          imei: vehicle!.regNo,
          onBackPressed: vehicleHealthOnBackPressed,
          onHealthDetailPressed: vehicleHealthDetailPressed,
          vehicle: vehicle!,
        );
      });
    }
  }

  void openProfileHome() {
    setState(() {
      activeScreen = ProfileHomeFactory.createProfileHome(
        openDetails: openProfileDetails,
        openHelp: openHelpPage,
        openSetting: openSetting,
        openAboutVehicle: openAboutVehiclePage,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Padding(
      padding: EdgeInsets.only(top: 0 * dimensions.height),
      child: activeScreen,
    );
  }
}
