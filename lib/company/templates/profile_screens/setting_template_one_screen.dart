import 'package:flutter/material.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/widgets/profile/select_home_display_name.dart';
import 'package:nds_app/widgets/profile/select_vehcile_visibility_range.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../common/image_urls.dart';

import '../../baseScreens/profile_screens/base_profile_screens.dart';

/// Template for setting screens
/// Contains all the setting functionality and UI
/// Used by all company that share the same setting template
class SettingTemplateOneScreen extends BaseSettingScreen {
  const SettingTemplateOneScreen({
    super.key,
    required super.onBackPressed,
  });

  @override
  State<SettingTemplateOneScreen> createState() =>
      _SettingTemplateOneScreenState();
}

class _SettingTemplateOneScreenState extends State<SettingTemplateOneScreen> {
  late int selectRange;
  late String selectHomeDisplayName;
  late void Function() onBackPressed;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Setting Screen B2C',
      'screen_class': widget.runtimeType.toString(),
    });

    onBackPressed = widget.onBackPressed;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return FutureBuilder(
        future: loadData(),
        builder: (context, snapshot) {
          Widget widget = Center(
            child: Image.asset(
              isTwoWheels
                  ? loaderGifImages['2Wheels']!
                  : loaderGifImages['3Wheels']!,
            ),
          );
          if (snapshot.connectionState == ConnectionState.done) {
            widget = Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(children: [
                Row(
                  children: [
                    GestureDetector(
                      onTap: onBackPressed,
                      child: const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Icon(
                          Icons.arrow_back_ios,
                          size: 15,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      profileScreen["menu3"]!,
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                  ],
                ),
                SizedBox(
                  height: 36 / 896 * dimensions.height,
                ),
                Visibility(
                  visible: !isB2CUser,
                  child: SelectVehicleVisibilityRange(
                    selectedRange: selectRange,
                    onRangeSelect: (value) async {
                      selectRange = value;
                    },
                  ),
                ),
                Visibility(
                  visible: isB2CUser,
                  child: Column(
                    children: [
                      SizedBox(
                        height: 14 / 896 * dimensions.height,
                      ),
                      SelectHomeDisplayName(
                        selectedHomeDisplayName: selectHomeDisplayName,
                        onHomeDisplayNameSelect: (value) async {
                          selectHomeDisplayName = value;
                        },
                      ),
                    ],
                  ),
                ),
                const Expanded(
                  child: SizedBox(),
                ),
                Align(
                  alignment: Alignment.center,
                  child: TextButton(
                    style: ButtonStyle(
                        alignment: Alignment.center,
                        padding: WidgetStatePropertyAll(EdgeInsets.symmetric(
                            horizontal: 16 / 414 * dimensions.width)),
                        backgroundColor: WidgetStatePropertyAll(
                            Theme.of(context).focusColor)),
                    onPressed: () async {
                      SharedPreferences pref =
                          await SharedPreferences.getInstance();
                      pref.setInt(vehicleVisibilityRangeKey, selectRange);
                      pref.setString(homeDisplayNameKey, selectHomeDisplayName);
                      onBackPressed.call();
                    },
                    child: Text(
                      profileScreen["button"]!,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ),
                SizedBox(
                  height: 0.07 * dimensions.height,
                ),
              ]),
            );
          }
          return widget;
        });
  }

  loadData() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    selectRange = pref.getInt(vehicleVisibilityRangeKey) ?? 10;
    selectHomeDisplayName =
        pref.getString(homeDisplayNameKey) ?? homeDisplayNameOptions[0];
  }
}
