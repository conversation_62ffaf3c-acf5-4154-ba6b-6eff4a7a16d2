import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/user_info.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/profile/edit_profile_image.dart';
import 'package:nds_app/widgets/profile/select_blood_group.dart';
import 'package:nds_app/widgets/profile/select_dob.dart';
import 'package:nds_app/widgets/profile/edit_profile_item.dart';
import 'package:nds_app/widgets/profile/select_gender.dart';
import '../../../../common/image_urls.dart';
import '../../../../main.dart';

import '../../baseScreens/profile_screens/base_profile_screens.dart';

/// Template for edit profile screens
/// Contains all the edit profile functionality and UI
/// Used by all company that share the same edit profile template
class EditProfileTemplateOneScreen extends BaseEditProfileScreen {
  const EditProfileTemplateOneScreen({
    super.key,
    required super.onBackPressed,
  });

  @override
  State<EditProfileTemplateOneScreen> createState() =>
      EditProfileTemplateOneScreenState();
}

class EditProfileTemplateOneScreenState
    extends State<EditProfileTemplateOneScreen> {
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _emergencyNumberController =
      TextEditingController();
  late FixedExtentScrollController _weightController;
  int lowerLimitsForWeight = 30;
  int upperLimitsForWeight = 150;

  String? dob;
  String? selectedGender;
  bool isLoading = false;
  File? pickedImage;
  Map<String, dynamic>? profile;
  late Color color;
  String? bloodGroup;
  int? weight;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Edit Profile Screen B2C',
      'screen_class': widget.runtimeType.toString(),
    });

    loadProfileDetails();
    // String hexColorInStr =
    //     sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    color = colorGrey800;
    //hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    Widget widgets = Center(
      child: Image.asset(
        isTwoWheels ? loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
      ),
    );
    Color bgColor = colorGrey100;
    ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();
    if (themeMode == ThemeMode.dark) {
      bgColor = colorBackgroundDarkMode;
    }

    if (!isLoading && profile != null) {
      widgets = Scaffold(
        body: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.topCenter,
            children: [
              Image.asset(
                profileScreenImages["profileBg"]!, //profileBg
                fit: BoxFit.fill,
                width: double.infinity,
              ),
              const SizedBox(
                height: 35,
              ),
              Container(
                margin: const EdgeInsets.only(
                  top: 150,
                ),
                decoration: BoxDecoration(
                  color: bgColor,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(20),
                    topLeft: Radius.circular(20),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      SizedBox(
                        height: 0.1 * dimensions.width,
                      ),
                      EditProfileItem(
                        title: profileScreen["titleFirstName"]!,
                        hint: profileScreen["hint1"]!,
                        controller: _firstNameController,
                        onTap: () {},
                        keyboardType: TextInputType.text,
                        maxLength: 20,
                      ),
                      EditProfileItem(
                        title: profileScreen["titleLastName"]!,
                        hint: profileScreen["hint2"]!,
                        controller: _lastNameController,
                        onTap: () {},
                        keyboardType: TextInputType.text,
                        maxLength: 20,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 32,
                          ),
                          Text(
                            profileScreen["detail2"]!,
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          SelectDOB(
                            dob: dob,
                            onDateSelect: (date) {
                              setState(() {
                                dob = date;
                              });
                            },
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 32,
                          ),
                          Text(
                            profileScreen["detail3"]!,
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          SelectGender(
                            selectedGender: selectedGender,
                            onGenderSelect: (value) {
                              setState(() {
                                selectedGender = value;
                              });
                            },
                          ),
                        ],
                      ),
                      EditProfileItem(
                        title: profileScreen["detail7"]!,
                        hint: profileScreen["hint8"]!,
                        controller: _addressController,
                        onTap: () {},
                        keyboardType: TextInputType.text,
                        maxLength: 80,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 32,
                          ),
                          Text(
                            profileScreen["detail8"]!,
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          InkWell(
                            onTap: () async {
                              bottomSheet(dimensions);
                            },
                            child: Container(
                              alignment: Alignment.centerLeft,
                              width: double.infinity,
                              height: 65 / 896 * dimensions.height,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12.0, vertical: 4),
                              margin: const EdgeInsets.only(top: 9),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8.0),
                                border: Border.all(color: colorGrey600),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    weight == null
                                        ? profileScreen["hint11"]!
                                        : (weight ?? 0).toString(),
                                    style: weight == null
                                        ? Theme.of(context)
                                            .textTheme
                                            .headlineSmall
                                        : Theme.of(context)
                                            .textTheme
                                            .headlineMedium,
                                  ),
                                  const Icon(Icons.expand_more)
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 32,
                          ),
                          Text(
                            profileScreen["detail9"]!,
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          SelectBloodGroup(
                            selectedBloodGroup: bloodGroup ?? "",
                            onBloodGroupSelect: (value) {
                              setState(() {
                                bloodGroup = value;
                              });
                            },
                          ),
                        ],
                      ),
                      EditProfileItem(
                        title: profileScreen["detail10"]!,
                        hint: profileScreen["hint9"]!,
                        controller: _emergencyNumberController,
                        onTap: () {},
                        keyboardType: TextInputType.phone,
                        maxLength: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 88.0),
                        child: SizedBox(
                          width: double.infinity,
                          height: 56,
                          child: CustomButton.elevated(
                            onPressed: () async {
                              await uploadFile(pickedImage);
                              await updateProfileDetails();
                            },
                            backgroundColor: color,
                            borderRadius: 5.0,
                            text: profileScreen["update"]!,
                            foregroundColor: colorWhite,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 100,
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                  top: 0.2 * dimensions.width,
                ),
                child: ClipOval(
                  child: EditProfileImage(
                      imageUrl: profile?["profileImageUrl"],
                      editIconBg: color,
                      pickedImage: pickedImage,
                      onImagePicked: handleImagePick),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: widget.onBackPressed,
                      child: const Icon(
                        Icons.arrow_back_ios,
                        size: 15,
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      profileScreen["title3"]!,
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return widgets;
  }

  void handleImagePick(imageFile) {
    setState(() {
      pickedImage = imageFile;
    });
  }

  Future<void> loadProfileDetails() async {
    setState(() {
      isLoading = true;
    });
    JsonDecoder decoder = const JsonDecoder();
    http.Response profileDetailsResponse =
        await BackendApi.initiateGetCall(ApiUrls.profileDetails);

    Map<String, dynamic> profileDetails =
        decoder.convert(profileDetailsResponse.body);

    _firstNameController.text = profileDetails["firstName"] ?? "";
    _lastNameController.text = profileDetails["lastName"] ?? "";
    _addressController.text = profileDetails["longAddress"] ?? "";
    _emergencyNumberController.text = (profileDetails["emergencyNumber"] ?? "")
        .toString()
        .replaceFirst("+91", "");
    weight = (profileDetails["weightInKg"] ?? 60.0).round();
    bloodGroup = profileDetails["bloodGroup"] ?? "";

    setState(() {
      dob = profileDetails["dob"] ?? "";
      selectedGender = profileDetails["sex"] ?? "";
      profile = profileDetails;
      isLoading = false;
    });
  }

  Future<void> updateProfileDetails() async {
    JsonDecoder decoder = const JsonDecoder();

    setState(() {
      isLoading = true;
    });

    final request = {
      "firstName": _firstNameController.text,
      "lastName": _lastNameController.text,
      "dob": dob,
      "sex": selectedGender,
      "address": _addressController.text,
      "weightInKg": weight,
      "bloodGroup": bloodGroup,
      "emergencyNumber": "+91${_emergencyNumberController.text}",
    };

    http.Response response = await BackendApi.initiatePutCall(
        ApiUrls.updateProfileDetails,
        body: request);
    if (response.statusCode == 200) {
      http.Response userInfoResponse =
          await BackendApi.initiateGetCall(ApiUrls.userInfo);

      Map<String, dynamic> userInfoDetails =
          decoder.convert(userInfoResponse.body);

      userInfo = UserInfo.fromJson(userInfoDetails);
      userName = userInfo?.firstName ?? userName;
      Future.delayed(const Duration(seconds: 2), () {
        CustomToast.message(profileScreen["update_success"]!);
      });
      widget.onBackPressed();
    } else {
      CustomToast.error(profileScreen["error"]!);
    }

    setState(() {
      isLoading = false;
    });
  }

  Future<void> uploadFile(File? image) async {
    setState(() {
      isLoading = true;
    });

    if (image?.path == null) {
      return;
    }

    http.StreamedResponse response =
        await BackendApi.initiateImageUpload(ApiUrls.uploadProfileImage, image);
    if (response.statusCode == 200) {
      CustomToast.message(profileScreen["success_msg"]!);
    } else {
      CustomToast.error(profileScreen["error_msg"]!);
    }

    setState(() {
      isLoading = false;
    });
  }

  Widget makeDismissible({required Widget child}) => GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => Navigator.of(context).pop(),
        child: GestureDetector(onTap: () {}, child: child),
      );

  Future bottomSheet(Dimensions dimensions) {
    return showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
        top: Radius.circular(20),
      )),
      context: context,
      builder: (BuildContext context) => makeDismissible(
        child: DraggableScrollableSheet(
          initialChildSize: 0.55,
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
              decoration: BoxDecoration(
                color: Theme.of(context).splashColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(
                    dimensions.width * 20 / 414,
                  ),
                  topRight: Radius.circular(
                    dimensions.width * 20 / 414,
                  ),
                ),
              ),
              child: Column(
                children: [
                  Container(
                    height: dimensions.height * 50 / 896,
                    width: dimensions.width,
                    decoration: BoxDecoration(
                      color: Theme.of(context).splashColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(
                          dimensions.width * 20 / 414,
                        ),
                        topRight: Radius.circular(
                          dimensions.width * 20 / 414,
                        ),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).shadowColor,
                          offset: const Offset(0.0, 1.0),
                          blurRadius: 1.0,
                        ),
                      ],
                    ),
                    child: Center(
                      child: SizedBox(
                        width: dimensions.width * 378 / 414,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              profileScreen['detail8']!,
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            InkWell(
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                child: const Icon(Icons.close)),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      getWeightScrollBox(),
                      SizedBox(
                        height: dimensions.height * 28 / 896,
                      ),
                      CustomButton.text(
                        onPressed: () async {
                          setState(() {});
                          Navigator.pop(context);
                        },
                        text: profileScreen["save_button"]!,
                        backgroundColor: Theme.of(context).focusColor,
                        padding: EdgeInsets.symmetric(
                            horizontal: 16 / 414 * dimensions.width),
                        child: Text(
                          profileScreen["save_button"]!,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: dimensions.width * 258 / 414),
                    child: GestureDetector(
                      onTap: () {},
                      child: Container(),
                    ),
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget getWeightScrollBox() {
    Dimensions dimensions = Dimensions(context);
    _weightController = FixedExtentScrollController(
        initialItem: weight! - lowerLimitsForWeight);
    _weightController.jumpToItem(weight! - lowerLimitsForWeight);
    return Padding(
      padding: EdgeInsets.fromLTRB(
        dimensions.width * 147 / 414,
        dimensions.height * 78 / 1072,
        dimensions.width * 147 / 414,
        dimensions.height * 28 / 1072,
      ),
      child: SizedBox(
          width: dimensions.width * 130 / 414,
          height: dimensions.height * 300 / 1072,
          child: ListWheelScrollView.useDelegate(
            onSelectedItemChanged: (value) {
              weight = value + lowerLimitsForWeight;
            },
            overAndUnderCenterOpacity: 0.4,
            itemExtent: 45,
            perspective: 0.0099,
            diameterRatio: 9,
            squeeze: 0.90,
            useMagnifier: true,
            magnification: 1.08,
            controller: _weightController,
            physics: const FixedExtentScrollPhysics(),
            childDelegate: ListWheelChildBuilderDelegate(
              childCount: upperLimitsForWeight - lowerLimitsForWeight + 1,
              builder: (context, index) {
                return Container(
                  padding: const EdgeInsets.symmetric(vertical: 5.0),
                  child: Center(
                    child: Text((lowerLimitsForWeight + index).toString(),
                        style: Theme.of(context)
                            .textTheme
                            .displayLarge!
                            .copyWith(fontSize: 28 / 414 * dimensions.width)),
                  ),
                );
              },
            ),
          )),
    );
  }
}
