import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_details_bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_page_viewed_event.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/utils/oval_shape_vehicle_base.dart';
import 'package:nds_app/widgets/vehicle/charge_detail.dart';
import 'package:nds_app/widgets/vehicle/distance_detail.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

import '../../baseScreens/vehicle/base_vehicle_details_page.dart';

/// Template for vehicle details page screens
/// Contains all the vehicle details page functionality and UI
/// Used by all company that share the same vehicle details page template
class VehicleDetailsPageTemplateOneScreen extends BaseVehicleDetailsPage {
  const VehicleDetailsPageTemplateOneScreen(
    Vehicle vehicle,
    void Function() onBackPressed,
    void Function() healthPressed, {
    Key? key,
  }) : super(
          key: key,
          vehicle: vehicle,
          onBackPressed: onBackPressed,
          healthPressed: healthPressed,
        );

  @override
  State<VehicleDetailsPageTemplateOneScreen> createState() =>
      _DetailsPageTemplateOneScreenState();
}

class _DetailsPageTemplateOneScreenState
    extends State<VehicleDetailsPageTemplateOneScreen> {
  late ThemeMode themeMode;
  late Color color = colorGrey800;
  late Future loadColor;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Vehicle Details Page Template One',
      'screen_class': widget.runtimeType.toString(),
    });

    themeMode = MyApp.of(context).getCurrentThemeMode();
    loadColor = loadColorFromSharedPreferences();
    context.read<VehicleDetailsBloc>().add(const ViewEvent(isViewed: true));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    void Function() goBack = widget.onBackPressed;
    void Function() goVehicleHealth = widget.healthPressed;
    Vehicle vehicle = widget.vehicle;
    bool isConnected = vehicle.endDate == null;

    String? imageUrl;

    vehicle.images?.forEach(
      (element) {
        if (element.tag == defaultImageTag) {
          imageUrl = element.url;
        }
      },
    );

    return FutureBuilder(
      builder: (context, snapshot) {
        Widget widget = Center(
          child: Image.asset(
            isTwoWheels
                ? loaderGifImages['2Wheels']!
                : loaderGifImages['3Wheels']!,
          ),
        );

        if (snapshot.connectionState == ConnectionState.done) {
          widget = Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Column(
              children: [
                Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        goBack();
                      },
                      icon: const Padding(
                        padding:
                            EdgeInsets.only(right: 8.0, top: 8.0, bottom: 8.0),
                        child: Icon(
                          Icons.arrow_back_ios,
                          size: 18,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      vehicleScreen["title2"]!,
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          vehicle.regNo,
                          style: Theme.of(context).textTheme.headlineLarge,
                        ),
                        Text(
                          vehicle.model,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        const SizedBox(height: 16),
                        Center(
                          child: SizedBox(
                            height: 150,
                            width: 152.22,
                            child: Stack(children: [
                              Positioned(
                                  top: 100 / 896 * dimensions.height,
                                  child: SizedBox(
                                    height: 45 / 896 * dimensions.height,
                                    width: 142 / 414 * dimensions.width,
                                    child: OvalShapeBase(
                                      width: 152 * dimensions.width,
                                      height: 45 * dimensions.height,
                                      gradientColors: [colorWhite, color],
                                    ),
                                  )),
                              Positioned(
                                  left: 12,
                                  child: SizedBox(
                                    height: 140 / 896 * dimensions.height,
                                    width: 128 / 414 * dimensions.width,
                                    child: imageUrl == null
                                        ? Icon(
                                            Icons.electric_bike,
                                            size: 128 / 414 * dimensions.width,
                                          )
                                        : Image.network(imageUrl!,
                                            fit: BoxFit.fill),
                                  )),
                            ]),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Center(
                            child: Text(
                          afterConnectionCompanyLabel,
                          style: Theme.of(context).textTheme.titleSmall,
                        )),
                        isConnected
                            ? const SizedBox()
                            : Center(
                                child: Text(
                                "${vehicleScreen["last_connected"]}: ${DateFormat('dd/MM/yyyy').format(
                                  DateTime.fromMillisecondsSinceEpoch(
                                      vehicle.endDate!),
                                )}",
                                style: Theme.of(context).textTheme.titleSmall,
                              )),
                        const SizedBox(height: 16.0),
                        Row(
                          children: [
                            isConnected
                                ? const SizedBox()
                                : ChargeDetail(
                                    backgroundColor: colorGrey800,
                                    borderColor: color,
                                    themeMode: themeMode,
                                    vehicle: vehicle),
                            isConnected
                                ? const SizedBox()
                                : const SizedBox(width: 16.0),
                            DistanceDetail(
                                backgroundColor: colorGrey800,
                                borderColor: color,
                                themeMode: themeMode,
                                vehicle: vehicle)
                          ],
                        ),
                        const SizedBox(height: 32.0),
                        Visibility(
                          visible: !isProdRedUser,
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(32, 8, 32, 8),
                            child: SizedBox(
                              width: double.infinity,
                              height: 56,
                              child: CustomButton.elevated(
                                onPressed: () async {
                                  goVehicleHealth();
                                },
                                backgroundColor: color,
                                borderRadius: 45.0,
                                elevation: 4,
                                text: vehicleScreen["vehicleHealth"]!,
                                foregroundColor: colorWhite,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 100 / 896 * dimensions.height,
                ),
              ],
            ),
          );
        }
        return widget;
      },
      future: loadColor,
    );
  }

  loadColorFromSharedPreferences() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    String hexColorInStr = pref.getString(vehicleThemeColorInHex) ?? "";
    setState(() {
      color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
    });
  }
}
