import 'package:flutter/material.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/main.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../factoryFiles/splash_screen_factory.dart';
import '../../widgets/splash/common_splash_widgets.dart';
import '../../baseScreens/splash/base_splash.dart';

/// Template One for splash screens (main splash)
/// Contains all the splash functionality and UI
/// Used by all company that share the same splash template
class SplashTemplateOneScreen extends BaseSplash {
  const SplashTemplateOneScreen({Key? key}) : super(key: key);

  @override
  SplashTemplateOneScreenState createState() => SplashTemplateOneScreenState();
}

class SplashTemplateOneScreenState extends State<SplashTemplateOneScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _controllerTwo;

  @override
  void initState() {
    super.initState();
    _navigatetohome();
    _controller = AnimationController(
      duration: const Duration(seconds: 4, milliseconds: 500),
      vsync: this,
    );

    _controllerTwo = AnimationController(
      duration: const Duration(seconds: 2, milliseconds: 0),
      vsync: this,
    );
    _controllerTwo.animateBack(1, duration: const Duration(seconds: 4));

    _controller.repeat();
  }

  _navigatetohome() {
    Future.delayed(const Duration(seconds: 4, milliseconds: 700), () async {
      sharedPreferences = await SharedPreferences.getInstance();
      if (isBottomSheetOpenNotifier.value == false) {
        Navigator.pushReplacement(
            // ignore: use_build_context_synchronously
            context,
            MaterialPageRoute(
                builder: (context) =>
                    SplashScreenFactory.createLoadingScreen()));
      }
    });
  }

  late final Animation<Offset> _offsetAnimation =
      CommonSplashWidgets.createSlideAnimation(_controllerTwo);

  @override
  void dispose() {
    _controller.dispose();
    _controllerTwo.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;

    return Scaffold(
      body: CommonSplashWidgets.buildConnectivityListener(
        context: context,
        controller: _controller,
        controllerTwo: _controllerTwo,
        onNavigateToLoading: () async {
          await Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (context) =>
                    SplashScreenFactory.createLoadingScreen()),
          );
        },
        child: CommonSplashWidgets.buildAnimatedLogoSection(
          context: context,
          controller: _controller,
          offsetAnimation: _offsetAnimation,
        ),
      ),
    );
  }
}
