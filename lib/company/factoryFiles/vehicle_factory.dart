import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/company/baseScreens/vehicle/base_vehicle.dart';
import 'package:nds_app/company/templates/vehicle/vehicle_template_one_screen.dart';

/// Factory class to create company-specific vehicle screens based on the current company configuration
class VehicleFactory {
  /// Creates and returns the appropriate vehicle widget based on the company name
  static Widget createVehicle({
    required Color color,
    required ColorType colorType,
  }) {
    // All company currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getVehicleTemplate(color: color, colorType: colorType);
  }

  /// Returns the appropriate vehicle template based on company validation requirements
  static BaseVehicle _getVehicleTemplate({
    required Color color,
    required ColorType colorType,
  }) {
    // Currently all company use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return VehicleTemplateOneScreen(color: color, colorType: colorType);
    }
  }

  /// Returns the company-specific vehicle class name for debugging purposes
  static String getVehicleClassName() {
    return 'VehicleTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
