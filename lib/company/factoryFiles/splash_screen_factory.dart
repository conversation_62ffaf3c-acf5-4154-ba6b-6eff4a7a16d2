import 'package:flutter/material.dart';

// Template imports
import 'package:nds_app/companies/baseScreens/login/base_login.dart';
import 'package:nds_app/company/widgets/login/login_template_one_screen.dart';
import 'package:nds_app/company/widgets/loading/loading_template_one_screen.dart';
import 'package:nds_app/company/widgets/splash/splash_template_one_screen.dart';
import 'package:nds_app/company/widgets/onboarding/onboarding_template_one_screen.dart';
import 'package:nds_app/company/widgets/otp/otp_template_one_screen.dart';
import 'package:nds_app/company/widgets/promotional/promotional_template_one_screen.dart';

// Branding
import '../../branding/branding.dart';
import 'package:nds_app/models/user_activity_setting.dart';

class SplashScreenFactory {
  static Widget createSplashScreen() {
    return const SplashTemplateOneScreen();
  }

  static Widget createLoadingScreen() {
    return const LoadingTemplateOneScreen();
  }


  static Widget createOnboardingScreen() {
    return const OnboardingTemplateOneScreen();
  }

  static Widget createPromotionalScreen() {
    return const PromotionalTemplateOneScreen();
  }

  static BaseLogin createLoginScreen(
      {required List<UserActivitySetting> settings}) {
    return LoginTemplateOneScreen(settings: settings);
  }

  static Widget createOtpScreen({
    required String phoneNumber,
    required String phoneNumText,
    required List<UserActivitySetting> settings,
  }) {
    // All company use the same OTP template
    return OtpTemplateOneScreen(
      phoneNumber: phoneNumber,
      phoneNumText: phoneNumText,
      settings: settings,
    );
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
