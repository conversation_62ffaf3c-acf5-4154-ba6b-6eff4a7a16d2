import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/configurable_home_view.dart';
import '../../branding/branding.dart';

/// Factory class to create company-specific home views using the configurable component
class HomeViewFactory {
  static Widget createHomeView({required Function dashboardAction}) {
    // Get the configuration for the current company
    HomeViewConfig config = HomeViewConfig.getConfigForCompany(companyName);

    // Return the configurable home view with company-specific configuration
    return ConfigurableHomeView(
      dashboardAction: dashboardAction,
      companyName: companyName,
      config: config,
    );
  }

  /// Returns the company-specific home view class name for debugging purposes
  static String getHomeViewClassName() {
    switch (companyName) {
      case 'b2c':
        return 'HomeViewB2C';
      case 'lapa':
        return 'HomeViewLapa';
      case 'nds':
        return 'HomeViewNDS';
      case 'prodred':
        return 'HomeViewProdRed';
      case 'Nichesolv':
        return 'HomeViewNichesolv';
      default:
        return 'HomeViewNDS';
    }
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'Nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
