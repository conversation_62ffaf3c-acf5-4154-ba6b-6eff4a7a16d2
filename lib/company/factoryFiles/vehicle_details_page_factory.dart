import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/company/baseScreens/vehicle/base_vehicle_details_page.dart';
import 'package:nds_app/company/templates/vehicle/vehicle_details_page_template_one_screen.dart';

/// Factory class to create company-specific vehicle details page screens based on the current company configuration
class VehicleDetailsPageFactory {
  /// Creates and returns the appropriate vehicle details page widget based on the company name
  static Widget createVehicleDetailsPage({
    required Vehicle vehicle,
    required void Function() onBackPressed,
    required void Function() healthPressed,
  }) {
    // All company currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getVehicleDetailsPageTemplate(
      vehicle: vehicle,
      onBackPressed: onBackPressed,
      healthPressed: healthPressed,
    );
  }

  /// Returns the appropriate vehicle details page template based on company validation requirements
  static BaseVehicleDetailsPage _getVehicleDetailsPageTemplate({
    required Vehicle vehicle,
    required void Function() onBackPressed,
    required void Function() healthPressed,
  }) {
    // Currently all company use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return VehicleDetailsPageTemplateOneScreen(
            vehicle, onBackPressed, healthPressed);
    }
  }

  /// Returns the company-specific vehicle details page class name for debugging purposes
  static String getVehicleDetailsPageClassName() {
    return 'VehicleDetailsPageTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
