import 'package:flutter/material.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/baseScreens/insights/base_insights.dart';
import 'package:nds_app/company/widgets/insights/insights_template_one_screen.dart';
import 'package:nds_app/company/widgets/insights/insights_template_two_screen.dart';

/// Factory class to create company-specific insights screens based on the current company configuration
class InsightsFactory {
  /// Creates and returns the appropriate insights widget based on the company name
  static Widget createInsights(Color color, ColorType colorType) {
    // Use company-specific validation to determine template
    return _getInsightsTemplate(color: color, colorType: colorType);
  }

  /// Returns the appropriate insights template based on company validation requirements
  static BaseInsights _getInsightsTemplate({
    required Color color,
    required ColorType colorType,
  }) {
    // Template selection based on company requirements:
    // Template One: ProdRed (simplified view - statistics only)
    // Template Two: All other company (full toggle functionality)
    switch (companyName) {
      case 'prodred':
        return InsightsTemplateOneScreen(color: color, colorType: colorType);
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'nichesolv':
      case 'Nichesolv':
      default:
        return InsightsTemplateTwoScreen(color: color, colorType: colorType);
    }
  }

  /// Returns the company-specific insights template class name for debugging purposes
  static String getInsightsClassName() {
    switch (companyName) {
      case 'prodred':
        return 'InsightsTemplateOneScreen';
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'nichesolv':
      case 'Nichesolv':
      default:
        return 'InsightsTemplateTwoScreen';
    }
  }

  /// Returns the template type being used for the current company
  static String getTemplateType() {
    switch (companyName) {
      case 'prodred':
        return 'Template One (Statistics Only)';
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'nichesolv':
      case 'Nichesolv':
      default:
        return 'Template Two (Full Toggle Functionality)';
    }
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'Nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
