import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/company/baseScreens/profile_screens/base_profile_screens.dart';
import 'package:nds_app/company/templates/profile_screens/profile_home_template_one_screen.dart';

/// Factory class to create company-specific profile home screens based on the current company configuration
class ProfileHomeFactory {
  /// Creates and returns the appropriate profile home widget based on the company name
  static Widget createProfileHome({
    required void Function() openDetails,
    required void Function() openHelp,
    required void Function() openSetting,
    required void Function() openAboutVehicle,
  }) {
    // All company currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getProfileHomeTemplate(
      openDetails: openDetails,
      openHelp: openHelp,
      openSetting: openSetting,
      openAboutVehicle: openAboutVehicle,
    );
  }

  /// Returns the appropriate profile home template based on company validation requirements
  static BaseProfileHomeScreen _getProfileHomeTemplate({
    required void Function() openDetails,
    required void Function() openHelp,
    required void Function() openSetting,
    required void Function() openAboutVehicle,
  }) {
    // Currently all company use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return ProfileHomeTemplateOneScreen(
          openDetails: openDetails,
          openHelp: openHelp,
          openSetting: openSetting,
          openAboutVehicle: openAboutVehicle,
        );
    }
  }

  /// Returns the company-specific profile home class name for debugging purposes
  static String getProfileHomeClassName() {
    return 'ProfileHomeTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
