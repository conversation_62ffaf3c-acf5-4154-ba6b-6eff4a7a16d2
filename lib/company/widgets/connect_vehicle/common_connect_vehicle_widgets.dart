import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';

import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/utils/container_grid.dart';
import 'package:nds_app/widgets/connect/connect_view_toggle_button.dart';

/// Common connect vehicle templates that can be reused across all company connect vehicle screens
class CommonConnectVehicleWidgets {
  /// Builds the close button for the top right corner
  static Widget buildCloseButton({
    required BuildContext context,
    required double width,
    required double height,
    required VoidCallback onClose,
  }) {
    return Align(
      alignment: Alignment.topRight,
      child: InkWell(
        onTap: onClose,
        child: Padding(
          padding: EdgeInsets.all(8 / 414 * width),
          child: Si<PERSON><PERSON><PERSON>(
            height: 24 / 896 * height,
            width: 24 / 414 * width,
            child: const FittedBox(
              fit: BoxFit.fill,
              child: Icon(Icons.close, color: colorGrey800),
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the toggle button section
  static Widget buildToggleSection({
    required Color color,
    required ColorType colorType,
  }) {
    return ConnectViewToggleButton(
      color: color,
      colorType: colorType,
    );
  }

  /// Builds the complete QR scanner view with grid overlay and flash button
  static Widget buildQrScannerView({
    required BuildContext context,
    required Dimensions dimensions,
    required Widget qrView,
    required bool isFlashOn,
    required VoidCallback onFlashToggle,
    required ThemeMode themeMode,
  }) {
    final height = dimensions.height;
    final width = dimensions.width;

    return Stack(
      children: [
        // QR Scanner Container
        Align(
          alignment: Alignment.center,
          child: Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(top: 260 / 896 * height),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(8 / 414 * width),
            ),
            height: 284 / 896 * height,
            width: 284 / 414 * width,
            child: qrView,
          ),
        ),
        // Grid Overlay
        Align(
          alignment: Alignment.center,
          child: Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(top: 260 / 896 * height),
            child: CustomPaint(
              painter: GridPainter(gridLineColor: colorGrey500, gridCount: 3),
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(8 / 414 * width),
                ),
                height: 284 / 896 * height,
                width: 284 / 414 * width,
              ),
            ),
          ),
        ),
        // QR Scanner Icon and Text
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 140 / 896 * height, width: width),
            SizedBox(
              height: 36 / 896 * height,
              width: 36 / 414 * width,
              child: const FittedBox(
                fit: BoxFit.fill,
                child: Icon(Icons.qr_code_scanner_sharp, color: colorGrey800),
              ),
            ),
            SizedBox(height: 8 / 896 * height),
            Text(
              connectVehicleText['text6']!,
              style: poppinsTextStyle(
                12 / 414 * width,
                colorGrey800,
                FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        // Styled Flash Button
        buildStyledFlashButton(
          dimensions: dimensions,
          isFlashOn: isFlashOn,
          onFlashToggle: onFlashToggle,
          themeMode: themeMode,
        ),
      ],
    );
  }

  /// Builds the styled flash button with gradients and shadows
  static Widget buildStyledFlashButton({
    required Dimensions dimensions,
    required bool isFlashOn,
    required VoidCallback onFlashToggle,
    required ThemeMode themeMode,
  }) {
    return Align(
      alignment: Alignment.center,
      child: GestureDetector(
        onTap: onFlashToggle,
        child: Container(
          margin: EdgeInsets.only(top: 610 / 896 * dimensions.height),
          alignment: Alignment.center,
          width: 100 / 414 * dimensions.width,
          height: 100 / 414 * dimensions.width,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: themeMode == ThemeMode.light ? colorWhite : colorBlack,
            border: Border.all(color: colorGrey300, width: 1),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: themeMode == ThemeMode.light
                  ? [colorWhite, colorGrey200]
                  : [colorBlack, colorGrey700],
            ),
            boxShadow: themeMode == ThemeMode.light
                ? [
                    BoxShadow(
                      color: colorGrey300.withOpacity(0.7),
                      offset: const Offset(6, 6),
                      spreadRadius: 3,
                      blurRadius: 5,
                    ),
                    BoxShadow(
                      color: colorWhite.withOpacity(0.7),
                      offset: const Offset(-6, -6),
                      spreadRadius: 3,
                      blurRadius: 5,
                    ),
                  ]
                : [
                    BoxShadow(
                      color: colorGrey700.withOpacity(0.7),
                      offset: const Offset(6, 6),
                      spreadRadius: 3,
                      blurRadius: 5,
                    ),
                    BoxShadow(
                      color: colorBlack.withOpacity(0.7),
                      offset: const Offset(-6, -6),
                      spreadRadius: 3,
                      blurRadius: 5,
                    ),
                  ],
          ),
          child: Center(
            child: SizedBox(
              width: 48 / 414 * dimensions.width,
              height: 48 / 414 * dimensions.width,
              child: FittedBox(
                child: Icon(
                  isFlashOn ? Icons.flashlight_on : Icons.flashlight_off,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the complete chassis code input view
  static Widget buildChassisCodeView({
    required BuildContext context,
    required double height,
    required double width,
    required ThemeMode themeMode,
    required Widget textFieldContainers,
    required Stream<String> messageStream,
    required String? message,
  }) {
    return Column(
      children: [
        SizedBox(height: 225 / 896 * height),
        SizedBox(
          width: 44 / 414 * width,
          height: 44 / 896 * height,
          child: Image.asset(
            connectVehicleImages["password"]!,
            color: Theme.of(context).textTheme.headlineLarge?.color,
            fit: BoxFit.fill,
          ),
        ),
        SizedBox(height: 8 / 896 * height),
        Text(
          connectVehicleText["text5"]!,
          style: Theme.of(context).textTheme.labelMedium,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8 / 896 * height),
        Text(
          connectVehicleText["text2"]!,
          style: Theme.of(context).textTheme.labelMedium,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 32 / 896 * height),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [textFieldContainers],
        ),
        SizedBox(height: 16 / 896 * height),
        StreamBuilder<String>(
          stream: messageStream,
          initialData: "",
          builder: (context, snapshot) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(width: 50 / 414 * width),
                Text(
                  snapshot.data != "" ? (message ?? "") : "",
                  style: poppinsTextStyle(
                    12 / 414 * width,
                    colorRed,
                    FontWeight.w200,
                  ),
                ),
              ],
            );
          },
        ),
        SizedBox(height: 16 / 896 * height),
      ],
    );
  }

  /// Builds styled text field containers for chassis code input
  static Widget buildTextFieldContainers({
    required BuildContext context,
    required double width,
    required double height,
    required ThemeMode themeMode,
    required List<TextEditingController> controllers,
    required List<FocusNode> focusNodes,
    required List<Stream<String>> streams,
    required Function(String, int) onChanged,
  }) {
    List<Widget> widgets = [];
    for (int i = 0; i < controllers.length; i++) {
      widgets.add(
        StreamBuilder<String>(
          stream: streams[i],
          initialData: "",
          builder: (context, snapshot) {
            return Container(
              width: 58 / 414 * width,
              height: 80 / 896 * height,
              decoration: BoxDecoration(
                color:
                    themeMode == ThemeMode.light ? colorGrey200 : colorGrey600,
                border: Border.all(color: colorGrey300, width: 1.4),
                boxShadow: [
                  BoxShadow(
                    color: themeMode == ThemeMode.light
                        ? colorGrey200
                        : colorGrey600,
                    offset: const Offset(-3, -3),
                    spreadRadius: 3,
                    blurRadius: 3,
                  )
                ],
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Center(
                child: TextField(
                  style: Theme.of(context)
                      .primaryTextTheme
                      .headlineLarge
                      ?.copyWith(
                        fontSize: 48 / 414 * width,
                        fontWeight: FontWeight.w400,
                      ),
                  maxLength: 1,
                  textAlign: TextAlign.center,
                  textAlignVertical: TextAlignVertical.center,
                  keyboardType: TextInputType.number,
                  controller: controllers[i],
                  onChanged: (value) => onChanged(value, i),
                  decoration: InputDecoration(
                    counterText: "",
                    labelStyle: poppinsTextStyle(
                      0.029 * height,
                      colorBlack,
                      FontWeight.w700,
                    ),
                    border: InputBorder.none,
                  ),
                  focusNode: focusNodes[i],
                ),
              ),
            );
          },
        ),
      );
      if (i + 1 < controllers.length) {
        widgets.add(SizedBox(width: 16 / 414 * width));
      }
    }
    return Row(children: widgets);
  }

  /// Builds a QR view with scanner border
  static Widget buildQrView({
    required BuildContext context,
    required GlobalKey qrKey,
    required dynamic controller,
    required Function(dynamic) onDetect,
    required Function(Object, StackTrace) onDetectError,
  }) {
    Dimensions dimensions = Dimensions(context);
    var scanArea = dimensions.width;

    return Stack(
      children: [
        // Mobile Scanner Widget would be created in the template
        // This is just the border overlay
        Center(
          child: Container(
            width: 284 / 414 * scanArea,
            height: 284 / 414 * scanArea,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.secondary,
                width: 2,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
