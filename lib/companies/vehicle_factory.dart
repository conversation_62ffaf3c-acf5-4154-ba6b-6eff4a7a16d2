import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/enums/color_type.dart';

import 'b2c/views/vehicle/vehicle_b2c.dart';
import 'lapa/views/vehicle/vehicle_lapa.dart';
import 'nds/views/vehicle/vehicle_nds.dart';
import 'prodred/views/vehicle/vehicle_prodred.dart';
import 'nichesolv/views/vehicle/vehicle_nichesolv.dart';

class VehicleFactory {
  static Widget createVehicle({
    required Color color,
    required ColorType colorType,
  }) {
    switch (companyName) {
      case 'b2c':
        return VehicleB2C(color: color, colorType: colorType);
      case 'lapa':
        return VehicleLapa(color: color, colorType: colorType);
      case 'nds':
        return VehicleNDS(color: color, colorType: colorType);
      case 'prodred':
        return VehicleProdRed(color: color, colorType: colorType);
      case 'nichesolv':
        return VehicleNichesolv(color: color, colorType: colorType);
      default:
        return VehicleB2C(color: color, colorType: colorType);
    }
  }
}
