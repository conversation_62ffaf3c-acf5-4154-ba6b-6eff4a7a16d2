import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/widgets/vehicle/base_overview_ride_detail_screen.dart';
import 'package:nds_app/companies/widgets/vehicle/overview_ride_detail_screen_template_one_screen.dart';

/// Factory class to create company-specific overview ride detail screens based on the current company configuration
class OverviewRideDetailScreenFactory {
  /// Creates and returns the appropriate overview ride detail widget based on the company name
  static Widget createOverviewRideDetailScreen({
    required String testId,
  }) {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getOverviewRideDetailScreenTemplate(testId: testId);
  }

  /// Returns the appropriate overview ride detail template based on company validation requirements
  static BaseOverviewRideDetailScreen _getOverviewRideDetailScreenTemplate({
    required String testId,
  }) {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return OverviewRideDetailScreenTemplateOneScreen(testId: testId);
    }
  }

  /// Returns the company-specific overview ride detail class name for debugging purposes
  static String getOverviewRideDetailScreenClassName() {
    return 'OverviewRideDetailScreenTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
