import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';

import 'b2c/views/vehicle/overview_ride_detail_screen_b2c.dart';
import 'lapa/views/vehicle/overview_ride_detail_screen_lapa.dart';
import 'nds/views/vehicle/overview_ride_detail_screen_nds.dart';
import 'prodred/views/vehicle/overview_ride_detail_screen_prodred.dart';
import 'nichesolv/views/vehicle/overview_ride_detail_screen_nichesolv.dart';

class OverviewRideDetailScreenFactory {
  static Widget createOverviewRideDetailScreen({
    required String testId,
  }) {
    switch (companyName) {
      case 'b2c':
        return OverviewRideDetailScreenB2C(testId: testId);
      case 'lapa':
        return OverviewRideDetailScreenLapa(testId: testId);
      case 'nds':
        return OverviewRideDetailScreenNDS(testId: testId);
      case 'prodred':
        return OverviewRideDetailScreenProdRed(testId: testId);
      case 'nichesolv':
        return OverviewRideDetailScreenNichesolv(testId: testId);
      default:
        return OverviewRideDetailScreenB2C(testId: testId);
    }
  }
}
