import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_blocs.dart';
import 'package:nds_app/blocs/insight/statistics/statistics_state.dart';
import 'package:nds_app/blocs/insight/toggle/insight_toggle_bloc.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/enums/statistics_data_type.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/widgets/insights/fuel_savings_screen.dart';
import 'package:nds_app/widgets/insights/ride_details_fuel_saving_toggle_button.dart';
import 'package:nds_app/widgets/insights/statistics_screen.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

import '../../../../common/dimensions.dart';

class InsightsB2C extends StatefulWidget {
  final Color color;
  final ColorType colorType;
  const InsightsB2C({super.key, required this.color, required this.colorType});

  @override
  State<InsightsB2C> createState() => _InsightsB2CState();
}

class _InsightsB2CState extends State<InsightsB2C> {
  late Color color;
  late ColorType colorType;
  @override
  void initState() {
    color = widget.color;
    colorType = widget.colorType;
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Insight Screen B2C',
      'screen_class': widget.runtimeType.toString(),
    });
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );

    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Column(
      children: [
        isProdRedUser
            ? Expanded(
                child: StatisticsScreen(color: color, colorType: colorType),
              )
            : Expanded(
                child: Column(
                  children: [
                    BlocBuilder<StatisticsBloc, StatisticsState>(
                      builder: (context, state) {
                        return Visibility(
                          visible: state.statisticsDataType ==
                              StatisticsDataType.statistics,
                          child: Column(
                            children: [
                              SizedBox(
                                height: 22 / 896 * dimensions.height,
                              ),
                              ToggleButtonRideDetailsAndFuelSaving(
                                  color: color, colorType: colorType),
                              SizedBox(
                                height: 12 / 896 * dimensions.height,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    SizedBox(
                      height: 8 / 896 * dimensions.height,
                    ),
                    BlocBuilder<InsightToggleBloc, InsightToggleState>(
                        builder: (context, state) {
                      return state.isSwitchRight
                          ? const Expanded(child: FuelSavingsScreen())
                          : Expanded(
                              child: StatisticsScreen(
                                  color: color, colorType: colorType),
                            );
                    }),
                  ],
                ),
              ),
      ],
    );
  }
}
