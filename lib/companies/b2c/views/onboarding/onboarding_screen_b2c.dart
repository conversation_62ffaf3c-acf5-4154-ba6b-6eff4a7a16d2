import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/models/setting.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:http/http.dart' as http;
import '../../../../companies/splash_screen_factory.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../branding/branding.dart';
import '../../../../common/colors.dart';
import '../../../../common/dimensions.dart';
import '../../../../common/text_styles.dart';

/// B2C-specific onboarding screen
/// Uses B2C branding and can be customized for B2C-specific content
class OnboardingScreenB2C extends StatefulWidget {
  const OnboardingScreenB2C({Key? key}) : super(key: key);

  @override
  State<OnboardingScreenB2C> createState() => _OnboardingScreenB2CState();
}

class _OnboardingScreenB2CState extends State<OnboardingScreenB2C> {
  int currentIndex = 0;

  // B2C onboarding content (using centralized strings)
  List<Map<String, String>> onboardingData = [
    {
      'title': onBoardingScreenText['title1']!,
      'description': onBoardingScreenText['description1']!,
      'image': onboardingScreenImages['battery']!,
    },
    {
      'title': onBoardingScreenText['title2']!,
      'description': onBoardingScreenText['description2']!,
      'image': onboardingScreenImages['performance']!,
    }
  ];

  void onNext() async {
    if (currentIndex == onboardingData.length - 1) {
      await PreferenceHelper.setOnboardingDone();
      await _navigateToLoginScreen();
    } else {
      setState(() {
        currentIndex++;
      });
    }
  }

  void onSkip() async {
    await PreferenceHelper.setOnboardingDone();
    await _navigateToLoginScreen();
  }

  Future<void> _navigateToLoginScreen() async {
    try {
      // Fetch privacy policy and terms data
      final privacyPolicyResponse = await _getPrivacyPolicyResponse();
      final termsConditionsResponse = await _getTermsAndConditionsResponse();

      if (privacyPolicyResponse.statusCode == 200 &&
          termsConditionsResponse.statusCode == 200) {
        JsonDecoder decoder = const JsonDecoder();

        // Parse privacy policy
        Map<String, dynamic> privacyPolicyDetails =
            decoder.convert(privacyPolicyResponse.body);
        Setting privacyPolicy = Setting.fromJson(privacyPolicyDetails);

        // Parse terms and conditions
        Map<String, dynamic> termsConditionsResponseDetails =
            decoder.convert(termsConditionsResponse.body);
        Setting termsAndConditions =
            Setting.fromJson(termsConditionsResponseDetails);

        // Set global variables
        privacyPolicySetting = UserActivitySetting(
            activityType: ActivityType.privacyPolicyAcceptance,
            isNewSettingExist: false,
            message: privacyPolicy.message,
            value: privacyPolicy.value);

        termsAndConditionsSetting = UserActivitySetting(
            activityType: ActivityType.termsConditionsAcceptance,
            isNewSettingExist: false,
            message: termsAndConditions.message,
            value: termsAndConditions.value);

        // Navigate to login screen
        Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (_) => SplashScreenFactory.createLoginScreen(
                      settings: [
                        privacyPolicySetting!,
                        termsAndConditionsSetting!
                      ],
                    )));
      } else {
        // If API fails, fallback to loading screen
        Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (_) => SplashScreenFactory.createLoadingScreen()));
      }
    } catch (e) {
      // If any error occurs, fallback to loading screen
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (_) => SplashScreenFactory.createLoadingScreen()));
    }
  }

  Future<http.Response> _getPrivacyPolicyResponse() async {
    return await BackendApi.initiateGetCall(
      ApiUrls.setting,
      params: {
        "settingType": "privacy_policy",
        "organisationId": organisationId
      },
    );
  }

  Future<http.Response> _getTermsAndConditionsResponse() async {
    return await BackendApi.initiateGetCall(
      ApiUrls.setting,
      params: {
        "settingType": "terms_and_conditions",
        "organisationId": organisationId
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(14.0),
                  child: Text(
                    onboardingData[currentIndex]['title']!,
                    style: poppinsTextStyle(24 / 414 * dimensions.width,
                        loginThemeColor, FontWeight.w900),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Text(
                    onboardingData[currentIndex]['description']!,
                    style: poppinsTextStyle(20 / 414 * dimensions.width,
                        colorBlack, FontWeight.w400),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 420 / 896 * dimensions.height,
              width: 380 / 414 * dimensions.width,
              child: Image.asset(
                onboardingData[currentIndex]['image']!,
                width: double.infinity,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Skip Button without Background and Grey Text
                  CustomButton.text(
                    onPressed: onSkip,
                    text: "Skip",
                    foregroundColor: Colors.grey,
                  ),

                  // Next Button with White Background and Black Text
                  CustomButton.elevated(
                    onPressed: onNext,
                    text: currentIndex == onboardingData.length - 1
                        ? "Finish"
                        : "Next",
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF3E4A5B),
                    elevation: 8,
                    borderRadius: 30,
                    //padding: const EdgeInsets.symmetric(
                    // horizontal: 30, vertical: 12),
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                  )
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            )
          ],
        ),
      ),
    );
  }
}

class PreferenceHelper {
  static Future<void> setOnboardingDone() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('onboarding_done', true);
  }

  static Future<bool> isOnboardingDone() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('onboarding_done') ?? false;
  }
}
