import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/vehicle_toogle_bloc.dart';
import 'package:nds_app/blocs/vehicle/toggle/vehicle_toogle_state.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_details_bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_details_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/companies/vehicle_connection_page_factory.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/widgets/vehicle/toggle_button_vehicle_and_trip.dart';
import 'package:nds_app/widgets/vehicle/trip_history_list.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

class VehicleB2C extends StatefulWidget {
  final Color color;
  final ColorType colorType;
  const VehicleB2C({super.key, required this.color, required this.colorType});

  @override
  State<VehicleB2C> createState() => _VehicleB2CState();
}

class _VehicleB2CState extends State<VehicleB2C> {
  late Color color;
  late ColorType colorType;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Vehicle Screen B2C',
      'screen_class': widget.runtimeType.toString(),
    });

    color = widget.color;
    colorType = widget.colorType;
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );

    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Padding(
      padding: EdgeInsets.only(top: 24 / 993 * dimensions.height),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          isB2CUser
              ? const TripHistoryList()
              : BlocBuilder<VehicleDetailsBloc, VehicleDetailsState>(
                  builder: (context, state) {
                    return Visibility(
                        visible: !state.isViewed,
                        child: Column(
                          children: [
                            ToggleButtonVehicleAndTrip(
                                color: color, colorType: colorType),
                            SizedBox(
                              height: 8 / 896 * dimensions.height,
                            ),
                          ],
                        ));
                  },
                ),
          Visibility(
            visible: !isB2CUser,
            child: BlocBuilder<VehicleDetailsBloc, VehicleDetailsState>(
              builder: (context, state) {
                return SizedBox(
                    height:
                        (state.isViewed ? 740 : 680) / 896 * dimensions.height,
                    child: BlocBuilder<VehicleToogleBloc, VehicleToogleState>(
                        builder: (context, state) {
                      return state.isSwitchRight
                          ? const TripHistoryList()
                          : VehicleConnectionPageFactory.createVehicleConnectionPage();
                    }));
              },
            ),
          )
        ],
      ),
    );
  }
}
