import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';

import 'b2c/views/profile/help_b2c.dart';
import 'lapa/views/profile/help_lapa.dart';
import 'nds/views/profile/help_nds.dart';
import 'prodred/views/profile/help_prodred.dart';
import 'nichesolv/views/profile/help_nichesolv.dart';

class HelpFactory {
  static Widget createHelp({
    required void Function() onBackPressed,
  }) {
    switch (companyName) {
      case 'b2c':
        return HelpPageB2C(onBackPressed: onBackPressed);
      case 'lapa':
        return HelpPageLapa(onBackPressed: onBackPressed);
      case 'nds':
        return HelpPageNDS(onBackPressed: onBackPressed);
      case 'prodred':
        return HelpPageProdRed(onBackPressed: onBackPressed);
      case 'nichesolv':
        return HelpPageNichesolv(onBackPressed: onBackPressed);
      default:
        return HelpPageB2C(onBackPressed: onBackPressed);
    }
  }
}
