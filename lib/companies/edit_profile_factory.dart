import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';

import 'b2c/views/profile/edit_profile_b2c.dart';
import 'lapa/views/profile/edit_profile_lapa.dart';
import 'nds/views/profile/edit_profile_nds.dart';
import 'prodred/views/profile/edit_profile_prodred.dart';
import 'nichesolv/views/profile/edit_profile_nichesolv.dart';

class EditProfileFactory {
  static Widget createEditProfile({
    required void Function() onBackPressed,
  }) {
    switch (companyName) {
      case 'b2c':
        return EditProfileB2C(onBackPressed: onBackPressed);
      case 'lapa':
        return EditProfileLapa(onBackPressed: onBackPressed);
      case 'nds':
        return EditProfileNDS(onBackPressed: onBackPressed);
      case 'prodred':
        return EditProfileProdRed(onBackPressed: onBackPressed);
      case 'nichesolv':
        return EditProfileNichesolv(onBackPressed: onBackPressed);
      default:
        return EditProfileB2C(onBackPressed: onBackPressed);
    }
  }
}
