import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';

import 'b2c/views/vehicle/vehicle_details_page_b2c.dart';
import 'lapa/views/vehicle/vehicle_details_page_lapa.dart';
import 'nds/views/vehicle/vehicle_details_page_nds.dart';
import 'prodred/views/vehicle/vehicle_details_page_prodred.dart';
import 'nichesolv/views/vehicle/vehicle_details_page_nichesolv.dart';

class VehicleDetailsPageFactory {
  static Widget createVehicleDetailsPage({
    required Vehicle vehicle,
    required void Function() onBackPressed,
    required void Function() healthPressed,
  }) {
    switch (companyName) {
      case 'b2c':
        return VehicleDetailsPageB2C(vehicle, onBackPressed, healthPressed);
      case 'lapa':
        return VehicleDetailsPageLapa(vehicle, onBackPressed, healthPressed);
      case 'nds':
        return VehicleDetailsPageNDS(vehicle, onBackPressed, healthPressed);
      case 'prodred':
        return VehicleDetailsPageProdRed(vehicle, onBackPressed, healthPressed);
      case 'nichesolv':
        return VehicleDetailsPageNichesolv(vehicle, onBackPressed, healthPressed);
      default:
        return VehicleDetailsPageB2C(vehicle, onBackPressed, healthPressed);
    }
  }
}
