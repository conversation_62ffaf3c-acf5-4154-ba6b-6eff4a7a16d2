import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';

import 'b2c/views/profile/profile_home_b2c.dart';
import 'lapa/views/profile/profile_home_lapa.dart';
import 'nds/views/profile/profile_home_nds.dart';
import 'prodred/views/profile/profile_home_prodred.dart';
import 'nichesolv/views/profile/profile_home_nichesolv.dart';

class ProfileHomeFactory {
  static Widget createProfileHome({
    required void Function() openDetails,
    required void Function() openHelp,
    required void Function() openSetting,
    required void Function() openAboutVehicle,
  }) {
    switch (companyName) {
      case 'b2c':
        return ProfileHomeB2C(
          openDetails: openDetails,
          openHelp: openHelp,
          openSetting: openSetting,
          openAboutVehicle: openAboutVehicle,
        );
      case 'lapa':
        return ProfileHomeLapa(
          openDetails: openDetails,
          openHelp: openHelp,
          openSetting: openSetting,
          openAboutVehicle: openAboutVehicle,
        );
      case 'nds':
        return ProfileHomeNDS(
          openDetails: openDetails,
          openHelp: openHelp,
          openSetting: openSetting,
          openAboutVehicle: openAboutVehicle,
        );
      case 'prodred':
        return ProfileHomeProdRed(
          openDetails: openDetails,
          openHelp: openHelp,
          openSetting: openSetting,
          openAboutVehicle: openAboutVehicle,
        );
      case 'nichesolv':
        return ProfileHomeNichesolv(
          openDetails: openDetails,
          openHelp: openHelp,
          openSetting: openSetting,
          openAboutVehicle: openAboutVehicle,
        );
      default:
        return ProfileHomeB2C(
          openDetails: openDetails,
          openHelp: openHelp,
          openSetting: openSetting,
          openAboutVehicle: openAboutVehicle,
        );
    }
  }
}
