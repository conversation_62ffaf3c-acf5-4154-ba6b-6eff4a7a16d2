import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';

import 'b2c/views/profile/profile_b2c.dart';
import 'lapa/views/profile/profile_lapa.dart';
import 'nds/views/profile/profile_nds.dart';
import 'prodred/views/profile/profile_prodred.dart';
import 'nichesolv/views/profile/profile_nichesolv.dart';

class ProfileFactory {
  static Widget createProfile() {
    switch (companyName) {
      case 'b2c':
        return const ProfileScreenB2C();
      case 'lapa':
        return const ProfileScreenLapa();
      case 'nds':
        return const ProfileScreenNDS();
      case 'prodred':
        return const ProfileScreenProdRed();
      case 'nichesolv':
        return const ProfileScreenNichesolv();
      default:
        return const ProfileScreenB2C();
    }
  }
}
