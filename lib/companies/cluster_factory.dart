import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/companies/b2c/views/cluster/cluster_b2c.dart';
import 'package:nds_app/companies/lapa/views/cluster/cluster_lapa.dart';
import 'package:nds_app/companies/nds/views/cluster/cluster_nds.dart';
import 'package:nds_app/companies/prodred/views/cluster/cluster_prodred.dart';
import 'package:nds_app/companies/nichesolv/views/cluster/cluster_nichesolv.dart';
import 'package:nds_app/screens/cluster.dart';

class ClusterFactory {
  static Widget createCluster({
    required VehicleInfo vehicleInfo,
    required String userName,
  }) {
    switch (companyName) {
      case 'b2c':
        return ClusterB2C(vehicleInfo: vehicleInfo, userName: userName);
      case 'lapa':
        return ClusterLapa(vehicleInfo: vehicleInfo, userName: userName);
      case 'nds':
        return ClusterNDS(vehicleInfo: vehicleInfo, userName: userName);
      case 'prodred':
        return ClusterProdRed(vehicleInfo: vehicleInfo, userName: userName);
      case 'nichesolv':
        return ClusterNichesolv(vehicleInfo: vehicleInfo, userName: userName);
      default:
        return Cluster(vehicleInfo: vehicleInfo, userName: userName);
    }
  }
}
