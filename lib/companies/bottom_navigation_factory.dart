import 'package:flutter/material.dart';
import 'package:nds_app/widgets/dashboard/bottom_navigation.dart';
import 'package:nds_app/models/enums/color_type.dart';
import '../branding/branding.dart';

/// Factory class for bottom navigation - now uses the reusable component
/// All companies use the same bottom navigation implementation
class BottomNavigationFactory {
  /// Creates and returns the getBody widget - now uses reusable component
  static Widget createGetBody(
      Function action, BuildContext context, Color color, ColorType colorType) {
    // All companies now use the same reusable getBody function
    return getBody(action, context, color, colorType);
  }

  /// Creates and returns the getCustomBottomNavigationBar widget - now uses reusable component
  static Widget createGetCustomBottomNavigationBar(
      BuildContext context, Function action, Color color, ColorType colorType) {
    // All companies now use the same reusable getCustomBottomNavigationBar function
    return getCustomBottomNavigationBar(context, action, color, colorType);
  }

  /// Returns the company-specific bottom navigation class name for debugging purposes
  static String getBottomNavigationClassName() {
    switch (companyName) {
      case 'b2c':
        return 'BottomNavigationB2C';
      case 'lapa':
        return 'BottomNavigationLapa';
      case 'nds':
        return 'BottomNavigationNDS';
      case 'prodred':
        return 'BottomNavigationProdRed';
      case 'Nichesolv':
        return 'BottomNavigationNichesolv';
      default:
        return 'BottomNavigationNDS';
    }
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'Nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
