import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';

import 'b2c/views/vehicle/vehicles_list_b2c.dart';
import 'lapa/views/vehicle/vehicles_list_lapa.dart';
import 'nds/views/vehicle/vehicles_list_nds.dart';
import 'prodred/views/vehicle/vehicles_list_prodred.dart';
import 'nichesolv/views/vehicle/vehicles_list_nichesolv.dart';

class VehiclesListFactory {
  static Widget createVehiclesList({
    required void Function(Vehicle vehicle) switchScreen,
  }) {
    switch (companyName) {
      case 'b2c':
        return VehiclesListB2C(switchScreen);
      case 'lapa':
        return VehiclesListLapa(switchScreen);
      case 'nds':
        return VehiclesListNDS(switchScreen);
      case 'prodred':
        return VehiclesListProdRed(switchScreen);
      case 'nichesolv':
        return VehiclesListNichesolv(switchScreen);
      default:
        return VehiclesListB2C(switchScreen);
    }
  }
}
