import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';

import 'b2c/views/profile/about_vehicle_screen_b2c.dart';
import 'lapa/views/profile/about_vehicle_screen_lapa.dart';
import 'nds/views/profile/about_vehicle_screen_nds.dart';
import 'prodred/views/profile/about_vehicle_screen_prodred.dart';
import 'nichesolv/views/profile/about_vehicle_screen_nichesolv.dart';

class AboutVehicleFactory {
  static Widget createAboutVehicle({
    required void Function() onBackPressed,
    required void Function() healthPressed,
  }) {
    switch (companyName) {
      case 'b2c':
        return AboutVehicleScreenB2C(
          onBackPressed: onBackPressed,
          healthPressed: healthPressed,
        );
      case 'lapa':
        return AboutVehicleScreenLapa(
          onBackPressed: onBackPressed,
          healthPressed: healthPressed,
        );
      case 'nds':
        return AboutVehicleScreenNDS(
          onBackPressed: onBackPressed,
          healthPressed: healthPressed,
        );
      case 'prodred':
        return AboutVehicleScreenProdRed(
          onBackPressed: onBackPressed,
          healthPressed: healthPressed,
        );
      case 'nichesolv':
        return AboutVehicleScreenNichesolv(
          onBackPressed: onBackPressed,
          healthPressed: healthPressed,
        );
      default:
        return AboutVehicleScreenB2C(
          onBackPressed: onBackPressed,
          healthPressed: healthPressed,
        );
    }
  }
}
