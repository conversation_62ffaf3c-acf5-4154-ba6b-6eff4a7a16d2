import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/widgets/profile_screens/base_profile_screens.dart';
import 'package:nds_app/companies/widgets/profile_screens/about_vehicle_screen_template_one_screen.dart';

/// Factory class to create company-specific about vehicle screens based on the current company configuration
class AboutVehicleFactory {
  /// Creates and returns the appropriate about vehicle widget based on the company name
  static Widget createAboutVehicle({
    required void Function() onBackPressed,
    required void Function() healthPressed,
  }) {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getAboutVehicleTemplate(
      onBackPressed: onBackPressed,
      healthPressed: healthPressed,
    );
  }

  /// Returns the appropriate about vehicle template based on company validation requirements
  static BaseAboutVehicleScreen _getAboutVehicleTemplate({
    required void Function() onBackPressed,
    required void Function() healthPressed,
  }) {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return AboutVehicleScreenTemplateOneScreen(
          onBackPressed: onBackPressed,
          healthPressed: healthPressed,
        );
    }
  }

  /// Returns the company-specific about vehicle class name for debugging purposes
  static String getAboutVehicleClassName() {
    return 'AboutVehicleScreenTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
