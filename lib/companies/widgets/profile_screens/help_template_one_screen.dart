import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

import 'base_profile_screens.dart';

/// Template for help screens
/// Contains all the help functionality and UI
/// Used by all companies that share the same help template
class HelpTemplateOneScreen extends BaseHelpScreen {
  const HelpTemplateOneScreen({
    super.key,
    required super.onBackPressed,
  });

  @override
  State<HelpTemplateOneScreen> createState() => _HelpTemplateOneScreenState();
}

class _HelpTemplateOneScreenState extends State<HelpTemplateOneScreen> {
  late Function() onBackPressed;
  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Help Screen B2C',
      'screen_class': widget.runtimeType.toString(),
    });

    onBackPressed = widget.onBackPressed;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: widget.onBackPressed,
                child: const Padding(
                  padding: EdgeInsets.all(12.0),
                  child: Icon(
                    Icons.arrow_back_ios,
                    size: 15,
                  ),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Text(
                profileScreen["menu4"]!,
                style: Theme.of(context).textTheme.headlineLarge,
              ),
            ],
          ),
          SizedBox(
            height: 24 / 896 * dimensions.height,
          ),
          getContactContaner(
              profileScreenImages['contact1']!,
              profileScreen['contact1']!,
              contactPhoneNumber,
              dimensions,
              context),
          SizedBox(
            height: 16 / 896 * dimensions.height,
          ),
          getContactContaner(profileScreenImages['contact2']!,
              profileScreen['contact2']!, contactMail, dimensions, context),
        ],
      ),
    );
  }

  Container getContactContaner(String imagePath, String title, String content,
      Dimensions dimensions, BuildContext context) {
    return Container(
      height: 81 / 896 * dimensions.height,
      width: 374 / 414 * dimensions.width,
      padding: EdgeInsets.all(16 / 414 * dimensions.width),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8 / 414 * dimensions.width),
          border: Border.all(
            color: Theme.of(context).highlightColor.withOpacity(0.25),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(2, 2))
          ]),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 48 / 896 * dimensions.height,
            width: 48 / 414 * dimensions.width,
            child: Image.asset(imagePath),
          ),
          SizedBox(
            width: 8 / 414 * dimensions.width,
          ),
          FittedBox(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: Theme.of(context).textTheme.labelSmall),
                Text(content, style: Theme.of(context).textTheme.displayMedium),
              ],
            ),
          )
        ],
      ),
    );
  }
}
