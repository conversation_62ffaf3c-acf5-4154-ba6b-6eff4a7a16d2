import 'package:flutter/material.dart';

/// Base class for about vehicle screens
abstract class BaseAboutVehicleScreen extends StatefulWidget {
  final void Function() onBackPressed;
  final void Function() healthPressed;

  const BaseAboutVehicleScreen({
    super.key,
    required this.onBackPressed,
    required this.healthPressed,
  });
}

/// Base class for edit profile screens
abstract class BaseEditProfileScreen extends StatefulWidget {
  final void Function() onBackPressed;

  const BaseEditProfileScreen({
    super.key,
    required this.onBackPressed,
  });
}

/// Base class for help screens
abstract class BaseHelpScreen extends StatefulWidget {
  final void Function() onBackPressed;

  const BaseHelpScreen({
    super.key,
    required this.onBackPressed,
  });
}

/// Base class for profile details screens
abstract class BaseProfileDetailsScreen extends StatefulWidget {
  final void Function() editProfile;
  final void Function() onBackPressed;

  const BaseProfileDetailsScreen({
    super.key,
    required this.editProfile,
    required this.onBackPressed,
  });
}

/// Base class for profile home screens
abstract class BaseProfileHomeScreen extends StatefulWidget {
  final void Function() openDetails;
  final void Function() openHelp;
  final void Function() openSetting;
  final void Function() openAboutVehicle;

  const BaseProfileHomeScreen({
    super.key,
    required this.openDetails,
    required this.openHelp,
    required this.openSetting,
    required this.openAboutVehicle,
  });
}

/// Base class for setting screens
abstract class BaseSettingScreen extends StatefulWidget {
  final void Function() onBackPressed;

  const BaseSettingScreen({
    super.key,
    required this.onBackPressed,
  });
}
