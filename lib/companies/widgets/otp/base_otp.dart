import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';

/// Abstract base class for all OTP verification screen templates
/// This serves as the common type for all OTP implementations
/// allowing for polymorphic usage in navigation and factory patterns
abstract class BaseOtp extends StatefulWidget {
  final String phoneNumber;
  final String phoneNumText;
  final List<UserActivitySetting> settings;

  const BaseOtp({
    Key? key,
    required this.phoneNumber,
    required this.phoneNumText,
    required this.settings,
  }) : super(key: key);
}
