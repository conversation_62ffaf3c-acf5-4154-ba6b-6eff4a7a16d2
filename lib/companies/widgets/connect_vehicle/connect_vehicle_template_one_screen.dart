import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:nds_app/blocs/connect/connect_events.dart';
import 'package:nds_app/blocs/connect/connect_stream.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_bloc.dart';
import 'package:nds_app/blocs/connect/toggle/connect_vehicle_toogle_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/companies/dashboard_factory.dart';
import 'package:nds_app/utils/container_grid.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/widgets/connect/connect_view_toggle_button.dart';
import 'package:vibration/vibration.dart';
import 'package:synchronized/synchronized.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import 'base_connect_vehicle.dart';

/// Template for connect vehicle screens
/// Contains all the connect vehicle functionality and UI
/// Used by all companies that share the same connect vehicle template
class ConnectVehicleTemplateOneScreen extends BaseConnectVehicle {
  const ConnectVehicleTemplateOneScreen({super.key});

  @override
  State<ConnectVehicleTemplateOneScreen> createState() =>
      _ConnectVehicleTemplateOneScreenState();
}

class _ConnectVehicleTemplateOneScreenState
    extends State<ConnectVehicleTemplateOneScreen> {
  final _streams = ConnectScreenStream();
  late List<TextEditingController> _codeContainerControllers;
  late List<FocusNode> _codeContainerFocusNodes;

  bool isConnectChassisCodeViewEnabled = false;
  String? message;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  MobileScannerController? controller;
  late Color color;
  late ColorType colorType;
  bool isFlashOn = false;

  @override
  void initState() {
    WakelockPlus.enable();
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Connect Vehicle Screen Template One',
      'screen_class': widget.runtimeType.toString()
    });
    controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.normal,
      formats: [BarcodeFormat.qrCode],
      facing: CameraFacing.back,
      torchEnabled: false,
    );
    super.initState();

    // Add a short delay to ensure UI is fully initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        // Ensure the UI is fully visible
      });
    });

    _codeContainerControllers = [];
    _codeContainerFocusNodes = [];

    for (int i = 0; i < _streams.streams.length; i++) {
      _codeContainerControllers.add(TextEditingController());
      _codeContainerFocusNodes.add(FocusNode());
    }
    // _codeContainerFocusNodes[0].requestFocus();
    _streams.code.listen((event) async {
      if (event.length == _streams.streams.length) {
        FocusManager.instance.primaryFocus?.unfocus();
        // ignore: use_build_context_synchronously
        if (mounted) {
          message = await DialogAction.connect.action(
            context: context,
            code: event,
          );
        }

        _streams.eventSink.add(MessageEvent(message: message ?? ""));
      } else {
        _streams.eventSink.add(MessageEvent(message: ""));
      }
    });
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );
    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    double height = dimensions.height;
    double width = dimensions.width;
    bool themeMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
        resizeToAvoidBottomInset: false,
        body: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: 20 / 414 * width, vertical: 54 / 896 * height),
          child: Column(
            children: [
              Stack(
                children: [
                  BlocBuilder<ConnectVehicleToogleBloc,
                      ConnectVehicleToogleState>(
                    builder: (context, state) {
                      return state.isSwitchRight
                          ? _buildChassisCodeView(height, width, themeMode)
                          : Stack(
                              children: [
                                Align(
                                  alignment: Alignment.center,
                                  child: Container(
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                        top: 260 / 896 * dimensions.height),
                                    decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .scaffoldBackgroundColor,
                                        borderRadius: BorderRadius.circular(
                                            8 / 414 * width)),
                                    height: 284 / 896 * height,
                                    width: 284 / 414 * width,
                                    child: _buildQrView(context),
                                  ),
                                ),
                                Positioned(
                                  top: 560 / 896 * height,
                                  left: 0,
                                  right: 0,
                                  child: Center(
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          isFlashOn = !isFlashOn;
                                        });
                                        controller?.toggleTorch();
                                      },
                                      child: Container(
                                        height: 60 / 896 * height,
                                        width: 60 / 414 * width,
                                        decoration: BoxDecoration(
                                          color: isFlashOn
                                              ? colorWhite
                                              : colorGrey800,
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        child: Icon(
                                          Icons.flash_on,
                                          color: isFlashOn
                                              ? colorGrey800
                                              : colorWhite,
                                          size: 30 / 414 * width,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                    },
                  ),
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: Icon(
                                Icons.arrow_back_ios,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            Text(
                              connectVehicleText["text1"]!,
                              style: urbanistTextStyle(
                                  20 / 414 * width,
                                  Theme.of(context).primaryColor,
                                  FontWeight.w700),
                            ),
                            const SizedBox(width: 24),
                          ],
                        ),
                        SizedBox(height: 40 / 896 * height),
                        ConnectViewToggleButton(
                          color: color,
                          colorType: colorType,
                        ),
                        SizedBox(height: 40 / 896 * height),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  Widget _buildChassisCodeView(double height, double width, bool themeMode) {
    return Column(
      children: [
        SizedBox(height: 40 / 896 * height),
        Text(
          connectVehicleText["text2"]!,
          style: urbanistTextStyle(
              16 / 414 * width, colorGrey600, FontWeight.w500),
        ),
        SizedBox(height: 40 / 896 * height),
        Container(
          height: 284 / 896 * height,
          width: 284 / 414 * width,
          decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(8 / 414 * width)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(height: 20 / 896 * height),
              Image.asset(
                connectVehicleImages["password"]!,
                height: 120 / 896 * height,
                width: 120 / 414 * width,
              ),
              SizedBox(height: 20 / 896 * height),
              Text(
                connectVehicleText["text4"]!,
                style: urbanistTextStyle(
                    16 / 414 * width, colorGrey600, FontWeight.w500),
              ),
              SizedBox(height: 20 / 896 * height),
              // Code input containers would go here
              // Using a simple row for now
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(
                    4,
                    (index) => Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            border: Border.all(color: colorGrey600),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: TextField(
                            controller: _codeContainerControllers[index],
                            focusNode: _codeContainerFocusNodes[index],
                            textAlign: TextAlign.center,
                            maxLength: 1,
                            onChanged: (value) => fillContainer(value, index),
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                              counterText: '',
                            ),
                          ),
                        )),
              ),
              SizedBox(height: 20 / 896 * height),
            ],
          ),
        ),
        SizedBox(height: 20 / 896 * height),
        StreamBuilder<String>(
            stream: _streams.message,
            initialData: "",
            builder: (context, snapshot) {
              return snapshot.data!.isNotEmpty
                  ? Text(
                      snapshot.data!,
                      style: urbanistTextStyle(
                          16 / 414 * width, colorRed, FontWeight.w500),
                    )
                  : const SizedBox();
            }),
      ],
    );
  }

  final Lock _lock = Lock();
  bool _isExecuting = false;

  Widget _buildQrView(BuildContext context) {
    // For this example we check how width or tall the device is and change the scanArea and overlay accordingly.
    // To ensure the Scanner view is properly sizes after rotation
    // we need to listen for Flutter SizeChanged notification and update controller
    return Stack(
      children: [
        MobileScanner(
          key: qrKey,
          controller: controller,
          onDetect: (BarcodeCapture capture) {
            final List<Barcode> barcodes = capture.barcodes;
            if (barcodes.isNotEmpty) {
              Vibration.vibrate(duration: 500, amplitude: 200);
              tryLogin(barcodes.first);
            }
          },
          onDetectError: (error, stackTrace) => _onPermissionSet(error),
        ),
      ],
    );
  }

  void _onPermissionSet(Object e) {
    log('Error during scanning: $e');
  }

  void fillContainer(String number, int index) {
    _streams.eventSink.add(FillDigitEvent(digit: number, index: index));
    if (number.isEmpty && index > 0) {
      _codeContainerFocusNodes[index - 1].requestFocus();
    } else if (index + 1 < _codeContainerFocusNodes.length) {
      _codeContainerFocusNodes[index + 1].requestFocus();
    }
  }

  Future<void> tryLogin(Barcode barcode) async {
    await _lock.synchronized(() async {
      if (_isExecuting) return;
      _isExecuting = true;

      try {
        if (Platform.isAndroid || Platform.isIOS) {
          await controller?.stop();
        }

        // Show loading indicator while connecting
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          },
        );

        // ignore: use_build_context_synchronously
        String? msg = await DialogAction.connect
            // ignore: use_build_context_synchronously
            .action(imei: barcode.rawValue, context: context);

        // Dialog is closed by the connect action

        if (msg == null) {
          closePageOperation(200);
        } else {
          _isExecuting = false;
          _streams.updateMessage(msg);
          // Reset camera to continue scanning
          if (Platform.isAndroid || Platform.isIOS) {
            await controller?.start();
          }
        }
      } catch (e) {
        _isExecuting = false;
        log('Error during connection: $e');
        // Reset camera to continue scanning
        if (Platform.isAndroid || Platform.isIOS) {
          await controller?.start();
        }
      }
    });
  }

  void closePageOperation(int delayInMilliseconds) async {
    await Future.delayed(Duration(milliseconds: delayInMilliseconds));
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => DashboardFactory.createDashboard()),
      );
    }
  }
}
