import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import '../common/cluster/common_cluster_widgets.dart';
import 'base_cluster.dart';

/// Template for cluster screens
/// Contains all the cluster functionality and UI
/// Used by all companies that share the same cluster template
class ClusterTemplateOneScreen extends BaseCluster {
  final VehicleInfo vehicleInfo;
  final String userName;

  const ClusterTemplateOneScreen({
    super.key,
    required this.vehicleInfo,
    required this.userName,
  });

  @override
  State<ClusterTemplateOneScreen> createState() =>
      _ClusterTemplateOneScreenState();
}

class _ClusterTemplateOneScreenState extends State<ClusterTemplateOneScreen> {
  @override
  void initState() {
    super.initState();
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': "Cluster Screen Template One",
      'screen_class': widget.runtimeType.toString(),
    });
  }

  @override
  Widget build(BuildContext context) {
    return CommonClusterWidgets.buildClusterLayout(
      context: context,
      vehicleInfo: widget.vehicleInfo,
      userName: widget.userName,
    );
  }
}
