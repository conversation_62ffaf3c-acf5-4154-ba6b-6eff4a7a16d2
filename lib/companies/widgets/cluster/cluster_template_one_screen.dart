import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/utils/log_screen_tracking_event.dart';
import 'package:nds_app/widgets/cluster/cluster_body.dart';
import 'package:nds_app/widgets/cluster/cluster_bottom_menu.dart';
import 'package:nds_app/widgets/cluster/cluster_title_row.dart';

import 'base_cluster.dart';

/// Template for cluster screens
/// Contains all the cluster functionality and UI
/// Used by all companies that share the same cluster template
class ClusterTemplateOneScreen extends BaseCluster {
  final VehicleInfo vehicleInfo;
  final String userName;

  const ClusterTemplateOneScreen({
    super.key,
    required this.vehicleInfo,
    required this.userName,
  });

  @override
  State<ClusterTemplateOneScreen> createState() => _ClusterTemplateOneScreenState();
}

class _ClusterTemplateOneScreenState extends State<ClusterTemplateOneScreen> {
  @override
  void initState() {
    super.initState();
    LogScreenTrackingEvent().logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': "Cluster Screen Template One",
      'screen_class': widget.runtimeType.toString(),
    });
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    bool isPortrait =
        MediaQuery.of(context).orientation == Orientation.portrait;
    return Scaffold(
      backgroundColor: colorGrey900,
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: isPortrait
                      ? 20
                      : 5 /
                          360 *
                          (isPortrait ? dimensions.width : dimensions.height),
                  vertical: isPortrait
                      ? 10
                      : 5 /
                          360 *
                          (isPortrait ? dimensions.width : dimensions.height)),
              child: ClusterTitleRow(
                isPortrait: isPortrait,
                userName: widget.userName,
              ),
            ),
            Expanded(
                child: ClusterBody(
              isPortrait: isPortrait,
              vehicleInfo: widget.vehicleInfo,
            )),
            ClusterBottomMenu(
              isPortrait: isPortrait,
            )
          ],
        ),
      ),
    );
  }
}
