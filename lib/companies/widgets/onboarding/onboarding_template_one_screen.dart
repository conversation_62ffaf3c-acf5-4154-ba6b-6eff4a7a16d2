import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/models/setting.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:http/http.dart' as http;
import '../../factoryFiles/splash_screen_factory.dart';
import '../../../common/dimensions.dart';
import '../common/preference_helper.dart';
import '../common/onboarding/common_onboarding_widgets.dart';
import 'base_onboarding.dart';

/// Template One for onboarding screens
/// Contains all the onboarding functionality and UI
/// Used by all companies that share the same onboarding template
class OnboardingTemplateOneScreen extends BaseOnboarding {
  const OnboardingTemplateOneScreen({Key? key}) : super(key: key);

  @override
  State<OnboardingTemplateOneScreen> createState() =>
      _OnboardingTemplateOneScreenState();
}

class _OnboardingTemplateOneScreenState
    extends State<OnboardingTemplateOneScreen> {
  int currentIndex = 0;

  // Onboarding content (using centralized strings)
  List<Map<String, String>> onboardingData =
      CommonOnboardingWidgets.getOnboardingData();

  void onNext() async {
    if (currentIndex == onboardingData.length - 1) {
      await PreferenceHelper.setOnboardingDone();
      await _navigateToLoginScreen();
    } else {
      setState(() {
        currentIndex++;
      });
    }
  }

  void onSkip() async {
    await PreferenceHelper.setOnboardingDone();
    await _navigateToLoginScreen();
  }

  Future<void> _navigateToLoginScreen() async {
    try {
      // Fetch privacy policy and terms data
      final privacyPolicyResponse = await _getPrivacyPolicyResponse();
      final termsConditionsResponse = await _getTermsAndConditionsResponse();

      if (privacyPolicyResponse.statusCode == 200 &&
          termsConditionsResponse.statusCode == 200) {
        JsonDecoder decoder = const JsonDecoder();

        // Parse privacy policy
        Map<String, dynamic> privacyPolicyDetails =
            decoder.convert(privacyPolicyResponse.body);
        Setting privacyPolicy = Setting.fromJson(privacyPolicyDetails);

        // Parse terms and conditions
        Map<String, dynamic> termsConditionsResponseDetails =
            decoder.convert(termsConditionsResponse.body);
        Setting termsAndConditions =
            Setting.fromJson(termsConditionsResponseDetails);

        // Set global variables
        privacyPolicySetting = UserActivitySetting(
            activityType: ActivityType.privacyPolicyAcceptance,
            isNewSettingExist: false,
            message: privacyPolicy.message,
            value: privacyPolicy.value);

        termsAndConditionsSetting = UserActivitySetting(
            activityType: ActivityType.termsConditionsAcceptance,
            isNewSettingExist: false,
            message: termsAndConditions.message,
            value: termsAndConditions.value);

        // Navigate to login screen
        Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (_) => SplashScreenFactory.createLoginScreen(
                      settings: [
                        privacyPolicySetting!,
                        termsAndConditionsSetting!
                      ],
                    )));
      } else {
        // If API fails, fallback to loading screen
        Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (_) => SplashScreenFactory.createLoadingScreen()));
      }
    } catch (e) {
      // If any error occurs, fallback to loading screen
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (_) => SplashScreenFactory.createLoadingScreen()));
    }
  }

  Future<http.Response> _getPrivacyPolicyResponse() async {
    return CommonOnboardingWidgets.getPrivacyPolicyResponse();
  }

  Future<http.Response> _getTermsAndConditionsResponse() async {
    return CommonOnboardingWidgets.getTermsAndConditionsResponse();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CommonOnboardingWidgets.buildTitleAndDescription(
              dimensions: dimensions,
              pageData: onboardingData[currentIndex],
            ),
            CommonOnboardingWidgets.buildImageSection(
              dimensions: dimensions,
              imagePath: onboardingData[currentIndex]['image']!,
            ),
            CommonOnboardingWidgets.buildNavigationButtons(
              onSkip: onSkip,
              onNext: onNext,
              isLastPage: currentIndex == onboardingData.length - 1,
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}
