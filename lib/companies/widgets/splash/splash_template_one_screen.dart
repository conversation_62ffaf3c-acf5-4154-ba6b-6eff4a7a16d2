import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../branding/branding.dart';
import '../../../companies/splash_screen_factory.dart';
import 'base_splash.dart';

Size displaySize(BuildContext context) {
  return MediaQuery.of(context).size;
}

double displayHeight(BuildContext context) {
  return displaySize(context).height;
}

double displayWidth(BuildContext context) {
  return displaySize(context).width;
}

/// Template One for splash screens (main splash)
/// Contains all the splash functionality and UI
/// Used by all companies that share the same splash template
class SplashTemplateOneScreen extends BaseSplash {
  const SplashTemplateOneScreen({Key? key}) : super(key: key);

  @override
  SplashTemplateOneScreenState createState() => SplashTemplateOneScreenState();
}

class SplashTemplateOneScreenState extends State<SplashTemplateOneScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _controllerTwo;

  @override
  void initState() {
    super.initState();
    _navigatetohome();
    _controller = AnimationController(
      duration: const Duration(seconds: 4, milliseconds: 500),
      vsync: this,
    );

    _controllerTwo = AnimationController(
      duration: const Duration(seconds: 2, milliseconds: 0),
      vsync: this,
    );
    _controllerTwo.animateBack(1, duration: const Duration(seconds: 4));

    _controller.repeat();
  }

  _navigatetohome() {
    Future.delayed(const Duration(seconds: 4, milliseconds: 700), () async {
      sharedPreferences = await SharedPreferences.getInstance();
      if (isBottomSheetOpenNotifier.value == false) {
        Navigator.pushReplacement(
            // ignore: use_build_context_synchronously
            context,
            MaterialPageRoute(
                builder: (context) =>
                    SplashScreenFactory.createLoadingScreen()));
      }
    });
  }

  late final Animation<Offset> _offsetAnimation = Tween<Offset>(
    begin: Offset.zero,
    end: const Offset(-0.5, 0.0),
  ).animate(CurvedAnimation(
    parent: _controllerTwo,
    curve: Curves.elasticIn,
  ));

  @override
  void dispose() {
    _controller.dispose();
    _controllerTwo.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final widthforImg = displayWidth(context) * 0.3;
    currentContext = context;
    return Scaffold(
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) async {
          if (state is InternetConnectivityFailure) {
            // Show the no internet connection modal here
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _controller.dispose();
              _controllerTwo.dispose();
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                // Once the bottom sheet is dismissed, reset the notifier
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (isBottomSheetOpenNotifier.value == true &&
              state is InternetConnectivitySuccess) {
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              isBottomSheetOpenNotifier.value = false;
              Navigator.of(context).pop();
              SnackBarMessage.message(noInternetConnectionText["text5"]!,
                  backOnlineColorGreen, context);
              await Navigator.pushReplacement(
                // ignore: use_build_context_synchronously
                context,
                MaterialPageRoute(
                    builder: (context) =>
                        SplashScreenFactory.createLoadingScreen()),
              );
            });
          }
        },
        child: Center(
            child: SlideTransition(
          position: _offsetAnimation,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                splashScreenLoadingPageCompanyLogoBg,
                width: widthforImg,
                fit: BoxFit.contain,
              ),
              AnimatedBuilder(
                animation: _controller,
                child: Image.asset(
                  splashScreenLoadingPageCircularWhite,
                  width: widthforImg * 0.85,
                  fit: BoxFit.contain,
                ),
                builder: (BuildContext context, Widget? child) {
                  return Transform.rotate(
                    angle: _controller.value * 4.0 * math.pi,
                    child: child,
                  );
                },
              ),
            ],
          ),
        )),
      ),
    );
  }
}
