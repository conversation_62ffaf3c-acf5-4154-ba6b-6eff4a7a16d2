import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle.dart';

/// Base class for vehicle health detail screens
/// This serves as a contract for all vehicle health detail template implementations
abstract class BaseVehicleHealthDetail extends StatefulWidget {
  final void Function() onBackPressed;
  final String imei;
  final String partType;
  final String partLabel;
  final Vehicle vehicle;

  const BaseVehicleHealthDetail({
    super.key,
    required this.onBackPressed,
    required this.imei,
    required this.partType,
    required this.partLabel,
    required this.vehicle,
  });
}
