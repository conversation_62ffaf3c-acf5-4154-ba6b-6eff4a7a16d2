import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/vehicle/ride_details/ride_details_toggle_bloc.dart';
import 'package:nds_app/blocs/vehicle/ride_details/ride_details_toggle_state.dart';

import 'package:nds_app/screens/vehicle/overview_ride_detail_screen.dart';
import 'package:nds_app/screens/vehicle/route_screen.dart';
import 'package:nds_app/widgets/vehicle/ride_details_toggle_button.dart';

import '../../../../common/strings.dart';

import '../../baseScreens/vehicle/base_ride_details_screen.dart';

/// Template for ride details screens
/// Contains all the ride details functionality and UI
/// Used by all companies that share the same ride details template
class RideDetailsScreenTemplateOneScreen extends BaseRideDetailsScreen {
  const RideDetailsScreenTemplateOneScreen({
    super.key,
    required super.trip,
    required super.color,
    required super.colorType,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RideDetailsToggleBloc(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            rideDetailsScreenText["text1"]!,
            style: Theme.of(context).textTheme.headlineLarge,
          ),
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        ),
        body: Column(
          children: [
            RideDetailsToggleButton(
              color: color,
              colorType: colorType,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: BlocBuilder<RideDetailsToggleBloc, RideDetailsToggleState>(
                builder: (context, state) {
                  return state.isSwitchRight
                      ? RouteScreen(trip: trip)
                      : OverviewRideDetailScreen(
                          testId: trip.testId?.toString() ?? '');
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
