import 'package:flutter/material.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/trip.dart';

/// Base class for ride details screens
/// This serves as a contract for all ride details template implementations
abstract class BaseRideDetailsScreen extends StatelessWidget {
  final Trip trip;
  final Color color;
  final ColorType colorType;

  const BaseRideDetailsScreen({
    super.key,
    required this.trip,
    required this.color,
    required this.colorType,
  });
}
