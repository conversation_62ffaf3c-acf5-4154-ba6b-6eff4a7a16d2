import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_details_bloc.dart';
import 'package:nds_app/blocs/vehicle/vehicle_page_details/vehicle_page_viewed_event.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/utils/concave_decoration.dart';
import 'package:nds_app/widgets/vehicle/search_input.dart';
import 'package:nds_app/common/image_urls.dart';

import 'base_vehicles_list.dart';

/// Template for vehicles list screens
/// Contains all the vehicles list functionality and UI
/// Used by all companies that share the same vehicles list template
class VehiclesListTemplateOneScreen extends BaseVehiclesList {
  const VehiclesListTemplateOneScreen(super.switchScreen, {super.key});

  @override
  State<StatefulWidget> createState() => VehiclesListTemplateOneScreenState();
}

class VehiclesListTemplateOneScreenState
    extends State<VehiclesListTemplateOneScreen> {
  List<Vehicle> filteredVehicles = [];
  List<Vehicle> loadedVehicles = [];
  Vehicle? connectedVehicle;
  bool isLoading = false;

  void searchHandler(query) {
    if (query.length > 2 || query == "") {
      loadData(query);
    }
  }

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Vehicles List Template One',
      'screen_class': widget.runtimeType.toString(),
    });

    loadData("");
    context.read<VehicleDetailsBloc>().add(const ViewEvent(isViewed: false));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    Function handleTap = widget.switchScreen;
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          connectedVehicle == null
              ? const SizedBox()
              : getConnectedVehicleContainer(
                  connectedVehicle!,
                  dimensions,
                ),
          SearchInput(onSearch: searchHandler),
          SizedBox(
            height: 8 / 896 * dimensions.height,
          ),
          Visibility(
              visible: isLoading,
              child: Center(
                child: Image.asset(
                  isTwoWheels
                      ? loaderGifImages['2Wheels']!
                      : loaderGifImages['3Wheels']!,
                ),
              )),
          Visibility(
              visible: !isLoading && loadedVehicles.isEmpty,
              child: Center(
                  child: Text(
                vehicleScreen["noVehicles"]!,
                style: Theme.of(context).textTheme.headlineMedium,
              ))),
          Visibility(
            visible: !isLoading && loadedVehicles.isNotEmpty,
            child: Expanded(
              child: SizedBox(
                height: 400 / 896 * dimensions.height,
                child: ListView.builder(
                  itemCount: loadedVehicles.length,
                  itemBuilder: (_, index) {
                    final Vehicle vehicle = loadedVehicles[index];

                    String? imageUrl;

                    vehicle.images?.forEach(
                      (element) {
                        if (element.tag == defaultImageTag) {
                          imageUrl = element.url;
                          debugPrint("Vehicle image URL: $imageUrl");
                        }
                      },
                    );

                    // Make sure we don't have empty string URLs
                    if (imageUrl != null && imageUrl!.isEmpty) {
                      imageUrl = null;
                    }
                    return vehicle.endDate != null
                        ? GestureDetector(
                            onTap: () {
                              handleTap(vehicle);
                            },
                            child: Container(
                              margin: EdgeInsets.only(
                                  bottom: 16,
                                  left: 6 / 414 * dimensions.width,
                                  right: 6 / 414 * dimensions.width),
                              decoration: BoxDecoration(
                                  color:
                                      Theme.of(context).scaffoldBackgroundColor,
                                  borderRadius: BorderRadius.circular(8.0),
                                  border:
                                      Border.all(width: 1, color: colorGrey200),
                                  boxShadow: [
                                    BoxShadow(
                                        color: colorBlack.withOpacity(0.25),
                                        offset: const Offset(1, 3),
                                        blurRadius: 3,
                                        spreadRadius: 1),
                                    BoxShadow(
                                        color: colorWhite.withOpacity(0.25),
                                        offset: const Offset(-1, -3),
                                        blurRadius: 3,
                                        spreadRadius: 1)
                                  ]),
                              child: Row(
                                children: [
                                  Container(
                                    width: 72 / 414 * dimensions.width,
                                    height: 85 / 896 * dimensions.height,
                                    decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .scaffoldBackgroundColor,
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        border: Border.all(
                                            width: 1, color: colorGrey300),
                                        boxShadow: [
                                          BoxShadow(
                                              color:
                                                  colorBlack.withOpacity(0.25),
                                              offset: const Offset(1, 3),
                                              blurRadius: 3,
                                              spreadRadius: 1),
                                          BoxShadow(
                                              color:
                                                  colorWhite.withOpacity(0.25),
                                              offset: const Offset(-1, -3),
                                              blurRadius: 3,
                                              spreadRadius: 1)
                                        ]),
                                    child: Center(
                                      child: SizedBox(
                                        width: 50 / 414 * dimensions.width,
                                        height: 54 / 896 * dimensions.height,
                                        child: imageUrl == null
                                            ? const Icon(Icons.electric_bike)
                                            : CachedNetworkImage(
                                                imageUrl: imageUrl!,
                                                fit: BoxFit.fill,
                                                progressIndicatorBuilder:
                                                    (context, url, progress) =>
                                                        Center(
                                                  child: SizedBox(
                                                    width: 20,
                                                    height: 20,
                                                    child:
                                                        CircularProgressIndicator(
                                                      value: progress.progress,
                                                      strokeWidth: 2.0,
                                                    ),
                                                  ),
                                                ),
                                                errorWidget: (context, url,
                                                        error) =>
                                                    const Icon(
                                                        Icons.electric_bike),
                                              ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                      width: 15.0 / 414 * dimensions.width),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                            alignment: Alignment.centerLeft,
                                            width: 165 / 414 * dimensions.width,
                                            height:
                                                32 / 896 * dimensions.height,
                                            child: FittedBox(
                                              child: Text(
                                                textAlign: TextAlign.start,
                                                vehicle.regNo,
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .labelMedium,
                                              ),
                                            ),
                                          ),
                                          Text(vehicle.model,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleSmall),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 42 / 896 * dimensions.height,
                                        width: 100 / 414 * dimensions.width,
                                        child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              Container(
                                                height: 42 /
                                                    896 *
                                                    dimensions.height,
                                                width:
                                                    85 / 414 * dimensions.width,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          4.0),
                                                  border: Border.all(
                                                    color: colorGrey300,
                                                    width: 1.0,
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                height: 41 /
                                                    896 *
                                                    dimensions.height,
                                                width:
                                                    84 / 414 * dimensions.width,
                                                margin:
                                                    const EdgeInsets.all(1.0),
                                                decoration: ConcaveDecoration(
                                                  alignment: MyApp.of(context)
                                                              .getCurrentThemeMode() ==
                                                          ThemeMode.dark
                                                      ? Alignment.topRight
                                                      : Alignment.topLeft,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            4 /
                                                                414 *
                                                                dimensions
                                                                    .width),
                                                  ),
                                                  depth: 3,
                                                  colors: [
                                                    colorWhite,
                                                    Colors.black
                                                        .withOpacity(0.25),
                                                  ],
                                                  opacity: 0.5,
                                                ),
                                              ),
                                              Text(
                                                DateFormat('dd MMM yy').format(
                                                  DateTime
                                                      .fromMillisecondsSinceEpoch(
                                                          vehicle.endDate!),
                                                ),
                                                textAlign: TextAlign.end,
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .titleSmall,
                                              ),
                                            ]),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox();
                  },
                ),
              ),
            ),
          ),
          SizedBox(
            height: 100 / 896 * dimensions.height,
          ),
        ],
      ),
    );
  }

  Widget getConnectedVehicleContainer(
    Vehicle vehicle,
    Dimensions dimensions,
  ) {
    String? imageUrl;

    vehicle.images?.forEach(
      (element) {
        if (element.tag == defaultImageTag) {
          imageUrl = element.url;
          debugPrint("Connected vehicle image URL: $imageUrl");
        }
      },
    );

    // Make sure we don't have empty string URLs
    if (imageUrl != null && imageUrl!.isEmpty) {
      imageUrl = null;
    }
    return GestureDetector(
      onTap: () {
        widget.switchScreen(vehicle);
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(8, 16, 8, 16),
        margin: EdgeInsets.only(
            bottom: 16,
            left: 6 / 414 * dimensions.width,
            right: 6 / 414 * dimensions.width),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(width: 2, color: colorGrey300)),
        child: Row(
          children: [
            SizedBox(
              // width: 50 / 414 * dimensions.width,
              height: 54 / 896 * dimensions.height,
              child: imageUrl == null
                  ? const Icon(Icons.electric_bike)
                  : CachedNetworkImage(
                      imageUrl: imageUrl!,
                      fit: BoxFit.fill,
                      progressIndicatorBuilder: (context, url, progress) =>
                          Center(
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            value: progress.progress,
                            strokeWidth: 2.0,
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.electric_bike),
                    ),
            ),
            Flexible(child: SizedBox(width: 20.0 / 414 * dimensions.width)),
            Container(
              alignment: Alignment.centerLeft,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    //width: 145 / 414 * dimensions.width,
                    height: 32 / 896 * dimensions.height,
                    child: FittedBox(
                      child: Text(
                        textAlign: TextAlign.start,
                        vehicle.regNo,
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                    ),
                  ),
                  Text(vehicle.model,
                      style: Theme.of(context).textTheme.titleSmall),
                ],
              ),
            ),
            Flexible(child: SizedBox(width: 20.0 / 414 * dimensions.width)),
            Container(
              alignment: Alignment.centerRight,
              child: Stack(
                children: [
                  Container(
                    height: 42 / 896 * dimensions.height,
                    width: 108 / 414 * dimensions.width,
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(20.0),
                      border: Border.all(width: 1, color: colorGrey300),
                      boxShadow: const [
                        BoxShadow(
                            color: colorWhite,
                            offset: Offset(0, 0),
                            blurRadius: 5,
                            spreadRadius: 1),
                      ],
                    ),
                  ),
                  Container(
                    height: 34 / 896 * dimensions.height,
                    width: 100 / 414 * dimensions.width,
                    margin: EdgeInsets.only(
                        left: 4 / 414 * dimensions.width,
                        top: 4 / 896 * dimensions.height),
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(20.0),
                      border: Border.all(width: 1, color: colorGrey300),
                      boxShadow: [
                        BoxShadow(
                            color: colorBlack.withOpacity(0.2),
                            offset: const Offset(0, 0),
                            blurRadius: 2,
                            spreadRadius: 1),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        vehicleScreen["connected"]!,
                        textAlign: TextAlign.end,
                        style: poppinsTextStyle(12 / 414 * dimensions.width,
                            colorGreenSuccess, FontWeight.w300),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<List<Vehicle>> loadData(String key) async {
    setState(() {
      isLoading = true;
    });
    List<Vehicle> vehicles = [];
    try {
      http.Response response = await BackendApi.initiateGetCall(
          ApiUrls.userVehicleConnections,
          params: {"key": key});
      JsonDecoder decoder = const JsonDecoder();
      List<dynamic> jsonList = decoder.convert(response.body);
      vehicles = jsonList.map((json) {
        Vehicle vehicle = Vehicle.fromJson(json);
        if (vehicle.endDate == null) {
          connectedVehicle = vehicle;
        }
        return vehicle;
      }).toList();
      setState(() {
        loadedVehicles = vehicles;
      });
    } catch (e) {
      setState(() {
        loadedVehicles = [];
      });
      debugPrint("_VehiclePageState loadData e = $e");
    } finally {
      setState(() {
        isLoading = false;
      });
    }
    return [];
  }
}
