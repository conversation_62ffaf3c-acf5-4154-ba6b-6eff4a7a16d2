import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/companies/vehicle_details_page_factory.dart';
import 'package:nds_app/companies/vehicles_list_factory.dart';
import 'package:nds_app/companies/vehicle_health_page_factory.dart';
import 'package:nds_app/companies/vehicle_health_detail_page_factory.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

/// Template for vehicle connection page screens
/// Contains all the vehicle connection page functionality and UI
/// Used by all companies that share the same vehicle connection page template
class VehicleConnectionPageTemplateOneScreen extends StatefulWidget {
  const VehicleConnectionPageTemplateOneScreen({super.key});

  @override
  State<VehicleConnectionPageTemplateOneScreen> createState() =>
      _VehicleConnectionPageTemplateOneScreenState();
}

class _VehicleConnectionPageTemplateOneScreenState
    extends State<VehicleConnectionPageTemplateOneScreen> {
  late Widget activeScreen;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Vehicle Connection Page Template One',
      'screen_class': widget.runtimeType.toString(),
    });

    activeScreen =
        VehiclesListFactory.createVehiclesList(switchScreen: switchScreen);
    super.initState();
  }

  void switchScreen(Vehicle v) {
    setState(() {
      activeScreen = VehicleDetailsPageFactory.createVehicleDetailsPage(
        vehicle: v,
        onBackPressed: onBackPressed,
        healthPressed: () => vehicleHealthPressed(v),
      );
    });
  }

  void onBackPressed() {
    setState(() {
      activeScreen =
          VehiclesListFactory.createVehiclesList(switchScreen: switchScreen);
    });
  }

  void vehicleHealthPressed(Vehicle v) {
    setState(() {
      activeScreen = VehicleHealthPageFactory.createVehicleHealthPage(
        imei: v.regNo,
        onBackPressed: () => vehicleHealthOnBackPressed(v),
        onHealthDetailPressed: (String partType, String partLabel) =>
            vehicleHealthDetailPressed(v, partType, partLabel),
        vehicle: v,
      );
    });
  }

  void vehicleHealthOnBackPressed(Vehicle v) {
    setState(() {
      activeScreen = VehicleDetailsPageFactory.createVehicleDetailsPage(
        vehicle: v,
        onBackPressed: onBackPressed,
        healthPressed: () => vehicleHealthPressed(v),
      );
    });
  }

  void vehicleHealthDetailPressed(
      Vehicle v, String partType, String partLabel) {
    setState(() {
      activeScreen =
          VehicleHealthDetailPageFactory.createVehicleHealthDetailPage(
        onBackPressed: () => vehicleHealthDetailOnBackPressed(v),
        imei: v.regNo,
        partType: partType,
        partLabel: partLabel,
        vehicle: v,
      );
    });
  }

  void vehicleHealthDetailOnBackPressed(Vehicle v) {
    setState(() {
      activeScreen = VehicleHealthPageFactory.createVehicleHealthPage(
        imei: v.regNo,
        onBackPressed: () => vehicleHealthOnBackPressed(v),
        onHealthDetailPressed: (String partType, String partLabel) =>
            vehicleHealthDetailPressed(v, partType, partLabel),
        vehicle: v,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return activeScreen;
  }
}
