import 'package:flutter/material.dart';
import 'package:nds_app/models/enums/color_type.dart';

/// Base class for vehicle screens
/// This serves as a contract for all vehicle template implementations
abstract class BaseVehicle extends StatefulWidget {
  final Color color;
  final ColorType colorType;
  
  const BaseVehicle({
    super.key,
    required this.color,
    required this.colorType,
  });
}
