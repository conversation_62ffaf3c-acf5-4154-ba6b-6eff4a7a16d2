import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/blocs/navigation/navigation_bar_stream.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_event.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/nearby_vehicle.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/streams/vehicle_status_data.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/alert/alert_message_with_close_button_action.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/companies/factoryFiles/bottom_navigation_factory.dart';

import '../../../common/constant.dart';
import '../../../common/shared_preferences_keys.dart';
import '../../../constant/api_urls.dart';
import '../../../constant/vehicle_status.dart';
import '../../../services/api_service.dart';
import '../../../services/log_screen_tracking_event.dart';
import '../common/dashboard/common_dashboard_widgets.dart';
import 'base_dashboard.dart';

/// Template for dashboard screens
/// Contains all the dashboard functionality and UI
/// Used by all companies that share the same dashboard template
class DashboardTemplateOneScreen extends BaseDashboard {
  const DashboardTemplateOneScreen({super.key});

  @override
  State<DashboardTemplateOneScreen> createState() =>
      _DashboardTemplateOneScreenState();
}

class _DashboardTemplateOneScreenState
    extends State<DashboardTemplateOneScreen> {
  bool isNavigated = false;
  late Color color;
  late ColorType colorType;
  final NavigationBarStream navigationBarStream = NavigationBarStream();
  final VehicleStatusDataStream _vehicleStatusDataStream =
      VehicleStatusDataStream();

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': "Dashboard Screen Template One",
      'screen_class': widget.runtimeType.toString(),
    });

    navigationBarStream.submitIndex(0);
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );
    color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
    currentMarkers = {};
    currentNearbyPOI = NearbyPOI();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (isVehicleInfoAlertMessageExist == true &&
          currentVehicleStatus == VehicleStatus.connected) {
        getAlertMessageWithCloseButtonAction(context, alertMessages['text1']!);
      } else {
        isVehicleInfoAlertMessageExist == false;
        refreshVehicleInfo();
      }
    });
    super.initState();
  }

  action() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    currentContext = context;
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      resizeToAvoidBottomInset: false,
      appBar: PreferredSize(
          preferredSize: Size.fromHeight(120 * dimensions.height),
          child: CommonDashboardWidgets.buildAppBar(
            dimensions: dimensions,
            action: action,
            context: context,
            userName: userName,
          )),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: StreamBuilder<VehicleStatus>(
          stream: _vehicleStatusDataStream.vehicleStatus,
          builder: (context, snapshot) {
            if (snapshot.data != null) {
              navigationBarStream.submitIndex(0);
              String hexColorInStr =
                  sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

              colorType = ColorType.values.firstWhere(
                (element) =>
                    element.toString() ==
                    sharedPreferences!.getString(vehicleThemeColorTypeKey),
                orElse: () => ColorType.normal,
              );

              color = hexColorInStr.isNotEmpty
                  ? hexColorInStr.toColor()
                  : colorGrey800;
              currentMarkers = {};
              currentNearbyPOI = NearbyPOI();
            }
            return BottomNavigationFactory.createGetCustomBottomNavigationBar(
                context, action, color, colorType);
          }),
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) {
          if (state is InternetConnectivityFailure) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (isBottomSheetOpenNotifier.value == true &&
              state is InternetConnectivitySuccess) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Navigator.of(context).pop();
              isBottomSheetOpenNotifier.value = false;
              SnackBarMessage.message(noInternetConnectionText["text5"]!,
                  backOnlineColorGreen, context);
            });
          }
        },
        child: SizedBox(
          height: 0.86 * dimensions.height,
          child: BottomNavigationFactory.createGetBody(
              action, context, color, colorType),
        ),
      ),
    );
  }

  Future<void> refreshVehicleInfo() async {
    try {
      context
          .read<EditRiderDropDownBloc>()
          .add(const EditRiderDropDownEvent(overlayEntry: null));
      http.Response response = await BackendApi.initiateGetCall(
        ApiUrls.vehicleInfo,
        params: {"imei": connectedVehicleImeiNo},
      );

      if (response.statusCode == 200) {
        JsonDecoder decoder = const JsonDecoder();
        Map<String, dynamic> vehicleInfoDetails =
            decoder.convert(response.body);
        vehicleInfoConstant = VehicleInfo.fromJson(vehicleInfoDetails);
      }
    } catch (e) {
      debugPrint('Error refreshing vehicle info: $e');
    }
  }
}
