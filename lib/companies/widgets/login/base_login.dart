import 'package:flutter/material.dart';
import 'package:nds_app/models/user_activity_setting.dart';

/// Abstract base class for all login screen templates
/// This serves as the common type for all login implementations
/// allowing for polymorphic usage in navigation and factory patterns
abstract class BaseLogin extends StatefulWidget {
  final List<UserActivitySetting> settings;

  const BaseLogin({
    Key? key,
    required this.settings,
  }) : super(key: key);
}
