import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/blocs/login/login_bloc.dart';
import 'package:nds_app/blocs/login/login_events.dart';
import 'package:nds_app/blocs/navigation/navigation_bar_stream.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/streams/privacy_policy_and_terms_check_box_data.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/companies/widgets/common/login/common_login_widgets.dart';
import 'package:nds_app/companies/splash_screen_factory.dart';
import 'base_login.dart';

class LoginTemplateOneScreen extends BaseLogin {
  const LoginTemplateOneScreen({
    Key? key,
    required List<UserActivitySetting> settings,
  }) : super(key: key, settings: settings);

  @override
  State<LoginTemplateOneScreen> createState() => _LoginTemplateOneScreenState();
}

class _LoginTemplateOneScreenState extends State<LoginTemplateOneScreen> {
  late Dimensions dimensions;
  final _bloc = LoginScreenBloc();
  final _mobileNumberController = TextEditingController();
  late List<UserActivitySetting> settings;
  NavigationBarStream navigationBarStream = NavigationBarStream();

  CheckBoxPrivacyPolicyAndTosStream stream =
      CheckBoxPrivacyPolicyAndTosStream();

  bool isPrivacyPolicyAndTocChecked = false;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Login Template One Screen',
      'screen_class': widget.runtimeType.toString(),
    });

    settings = widget.settings;
    stream.checkBox.listen((event) {
      setState(() {
        isPrivacyPolicyAndTocChecked = event;
      });
      _bloc.eventSink
          .add(EnterNumberEvent(mobileNumber: _mobileNumberController.text));
    });
    navigationBarStream.submitIndex(0);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    dimensions = Dimensions(context);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) async {
          if (state is InternetConnectivityFailure) {
            // Show the no internet connection modal here
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                // Once the bottom sheet is dismissed, reset the notifier
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (state is InternetConnectivitySuccess) {
            // Handle internet connection restored if needed
          }
        },
        child: Padding(
          padding: EdgeInsets.fromLTRB(
              0.057 * dimensions.width, 0.0, 0.057 * dimensions.width, 0.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 0.08 * dimensions.height,
              ),
              CommonLoginWidgets.buildLoginImageSection(),
              CommonLoginWidgets.buildWelcomeSection(dimensions),
              SizedBox(
                height: 0.02 * dimensions.height,
              ),
              CommonLoginWidgets.buildMobileNumberSection(
                dimensions: dimensions,
                bloc: _bloc,
                mobileNumberController: _mobileNumberController,
                fillNumber: fillNumber,
              ),
              SizedBox(
                height: 0.005 * dimensions.height,
              ),
              CommonLoginWidgets.buildPrivacyPolicySection(settings),
              SizedBox(
                height: 0.04 * dimensions.height,
              ),
              CommonLoginWidgets.buildLoginButton(
                bloc: _bloc,
                isPrivacyPolicyAndTocChecked: isPrivacyPolicyAndTocChecked,
                mobileNumberController: _mobileNumberController,
                settings: settings,
                context: context,
                onNavigateToOtp: navigateToOtpScreen,
              ),
              SizedBox(
                height: 0.01 * dimensions.height,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void fillNumber(String mobileNumber) {
    setState(() {
      _bloc.eventSink.add(EnterNumberEvent(mobileNumber: mobileNumber));
    });
  }

  void navigateToOtpScreen(String phoneNumber, String phoneNumText,
      List<UserActivitySetting> settings) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SplashScreenFactory.createOtpScreen(
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        ),
      ),
    );
  }
}
