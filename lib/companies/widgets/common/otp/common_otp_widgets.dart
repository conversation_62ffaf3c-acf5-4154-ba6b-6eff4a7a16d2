import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nds_app/blocs/otp/otp_bloc.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/enums/otp_status.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/widgets/common/custom_button.dart';

/// Common OTP widgets that can be reused across all company OTP verification screens
class CommonOtpWidgets {
  /// Builds the header section with back button and title
  static Widget buildHeaderSection({
    required Dimensions dimensions,
    required String phoneNumText,
    required VoidCallback onBackPressed,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 25 / 878 * dimensions.height),
        GestureDetector(
          onTap: onBackPressed,
          child: Icon(
            Icons.arrow_back_ios,
            color: loginThemeColor,
          ),
        ),
        SizedBox(height: 0.038 * dimensions.height),
        Text(
          otpVerificationScreen["text7"]!,
          style: urbanistTextStyle(
              20 / 414 * dimensions.width, loginThemeColor, FontWeight.w700),
        ),
        SizedBox(height: 0.014 * dimensions.height),
        Text(
          "${otpVerificationScreen["text8"]!} $phoneNumText",
          style: urbanistTextStyle(
              14 / 414 * dimensions.width, primaryBlueLight, FontWeight.w500),
        ),
        SizedBox(height: 0.04 * dimensions.height),
      ],
    );
  }

  /// Builds the OTP input containers
  static Widget buildOtpContainers({
    required Dimensions dimensions,
    required OtpScreenBloc bloc,
    required List<TextEditingController> controllers,
    required List<FocusNode> focusNodes,
    required Function(String, int) onChanged,
  }) {
    List<Widget> widgets = [];

    for (int i = 0; i < controllers.length; i++) {
      widgets.add(
        SizedBox(
          width: 0.12 * dimensions.width,
          child: StreamBuilder<String>(
              stream: bloc.streams[i],
              initialData: "",
              builder: (context, snapshot) {
                controllers[i].text = snapshot.data!;

                return Container(
                  decoration: BoxDecoration(
                      color: otpDigitContainerColor,
                      border: Border.all(
                          color: snapshot.data!.isNotEmpty
                              ? snapshot.data == OtpStatus.invalid.toString()
                                  ? colorRed
                                  : secondaryBlue
                              : unSelectedTextBoxColor,
                          width: 1.4),
                      borderRadius: BorderRadius.circular(8.0)),
                  height: 0.072 * dimensions.height,
                  child: Center(
                    child: TextField(
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                      ],
                      onTap: () {
                        controllers[i].selection = TextSelection(
                            baseOffset: controllers[i].text.length,
                            extentOffset: controllers[i].text.length);
                      },
                      onChanged: (value) => onChanged(value, i),
                      maxLength: 1,
                      textAlign: TextAlign.center,
                      style: urbanistTextStyle(0.029 * dimensions.height,
                          colorBlack, FontWeight.w700),
                      keyboardType: TextInputType.number,
                      controller: controllers[i],
                      focusNode: focusNodes[i],
                      decoration: InputDecoration(
                        counterText: "",
                        labelStyle: urbanistTextStyle(0.029 * dimensions.height,
                            colorBlack, FontWeight.w700),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                );
              }),
        ),
      );

      if (i + 1 < controllers.length) {
        widgets.add(SizedBox(width: 15 / 414 * dimensions.width));
      }
    }

    return Row(children: widgets);
  }

  /// Builds the resend OTP section
  static Widget buildResendSection({
    required bool isResendEnabled,
    required int countdown,
    required VoidCallback onResend,
  }) {
    return Row(
      children: [
        isResendEnabled
            ? Row(
                children: [
                  Text(
                    "${otpVerificationScreen["text3"]!} ",
                    style: urbanistTextStyle(
                      14 / 414 * 414,
                      primaryBlueLight,
                      FontWeight.w500,
                    ),
                  ),
                  GestureDetector(
                    onTap: onResend,
                    child: Text(
                      otpVerificationScreen["text4"]!,
                      style: urbanistTextStyle(
                        14 / 414 * 414,
                        secondaryBlue,
                        FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              )
            : Text(
                otpVerificationScreen["text9"]!
                    .replaceFirst("@value", '$countdown')
                    .toString(),
                style: urbanistTextStyle(
                  12 / 414 * 414,
                  otpVerificationScreenGrey,
                  FontWeight.w500,
                ),
              ),
      ],
    );
  }

  /// Builds the verify button
  static Widget buildVerifyButton({
    required OtpScreenBloc bloc,
    required bool isEnabled,
    required VoidCallback onVerify,
  }) {
    return StreamBuilder<String>(
      stream: bloc.otp,
      initialData: "",
      builder: (streamContext, snapshot) {
        return snapshot.data!.length == bloc.streams.length
            ? GestureDetector(
                onTap: onVerify,
                child: CustomButton.gesture(
                  text: otpVerificationScreen["text5"]!,
                  backgroundColor: loginThemeColor,
                  size: CustomButtonSize.fullWidth,
                  textColor: colorWhite,
                  fontWeight: FontWeight.w600,
                ))
            : CustomButton.gesture(
                text: otpVerificationScreen["text5"]!,
                backgroundColor: loginThemeColor,
                size: CustomButtonSize.fullWidth,
                textColor: colorWhite,
                fontWeight: FontWeight.w600,
              );
      },
    );
  }

  /// Helper method to start resend timer
  static Timer startResendTimer({
    required Function(bool, int) onTimerUpdate,
  }) {
    onTimerUpdate(false, 10);

    return Timer.periodic(const Duration(seconds: 1), (timer) {
      int countdown = 10 - timer.tick;
      if (countdown > 0) {
        onTimerUpdate(false, countdown);
      } else {
        onTimerUpdate(true, 0);
        timer.cancel();
      }
    });
  }

  /// Helper method to handle resend OTP
  static Future<void> handleResendOtp({
    required BuildContext context,
    required String phoneNumber,
    required List<TextEditingController> controllers,
    required Function() onStartTimer,
  }) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    // Clear text controllers
    for (var controller in controllers) {
      controller.clear();
    }

    await LoginService.sendLoginOtp(phoneNumber);

    if (context.mounted) {
      Navigator.pop(context);
      onStartTimer();
    }
  }
}
