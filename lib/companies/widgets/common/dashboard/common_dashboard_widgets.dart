import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/companies/select_fleet_factory.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/widgets/dashboard/action_button_row.dart';
import 'package:nds_app/companies/bottom_navigation_factory.dart';

import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';

/// Common dashboard widgets that can be reused across all company dashboard screens
class CommonDashboardWidgets {
  /// Builds the main dashboard scaffold with app bar and bottom navigation
  static Widget buildDashboardScaffold({
    required BuildContext context,
    required Dimensions dimensions,
    required VoidCallback action,
    required Color color,
    required ColorType colorType,
    required Widget body,
    required String userName,
  }) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      resizeToAvoidBottomInset: false,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(120 * dimensions.height),
        child: buildAppBar(
          dimensions: dimensions,
          action: action,
          context: context,
          userName: userName,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton:
          BottomNavigationFactory.createGetCustomBottomNavigationBar(
        context,
        action,
        color,
        colorType,
      ),
      body: buildConnectivityListener(
        context: context,
        child: SizedBox(
          height: 0.86 * dimensions.height,
          child: body,
        ),
      ),
    );
  }

  /// Builds the app bar with action button row
  static Widget buildAppBar({
    required Dimensions dimensions,
    required VoidCallback action,
    required BuildContext context,
    required String userName,
  }) {
    return SafeArea(
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          border: const Border(
            bottom: BorderSide(color: colorGrey400, width: 1.0),
          ),
        ),
        width: dimensions.width,
        height: 120 / 896 * dimensions.height,
        child: ActionButtonRow(
          isConnected: currentVehicleStatus == VehicleStatus.connected,
          connectAction: () async {
            if (currentVehicleStatus == VehicleStatus.disconnected) {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => SelectFleetFactory.createSelectFleet(),
                ),
              );
            } else {
              await DialogAction.disconnect.action(context: context);
            }
          },
          context: context,
          userName: userName,
          isDashboardActionRow: true,
        ),
      ),
    );
  }

  /// Builds the connectivity listener for handling internet connection changes
  static Widget buildConnectivityListener({
    required BuildContext context,
    required Widget child,
  }) {
    return BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
      listener: (context, state) {
        if (state is InternetConnectivityFailure) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            getBotttomNoInternetConnection(
              heading: noInternetConnectionText["text4"]!,
              context: context,
            ).then((_) {
              isBottomSheetOpenNotifier.value = false;
            });
          });
        } else if (isBottomSheetOpenNotifier.value == true &&
            state is InternetConnectivitySuccess) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pop();
            isBottomSheetOpenNotifier.value = false;
            SnackBarMessage.message(
              noInternetConnectionText["text5"]!,
              backOnlineColorGreen,
              context,
            );
          });
        }
      },
      child: child,
    );
  }

  /// Helper method to initialize dashboard colors and settings
  static Map<String, dynamic> initializeDashboardSettings() {
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    ColorType colorType = ColorType.values.firstWhere(
      (element) =>
          element.toString() ==
          sharedPreferences!.getString(vehicleThemeColorTypeKey),
      orElse: () => ColorType.normal,
    );

    Color color =
        hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;

    return {
      'color': color,
      'colorType': colorType,
      'hexColorInStr': hexColorInStr,
    };
  }
}
