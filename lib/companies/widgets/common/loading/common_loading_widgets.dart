import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/models/setting.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/widgets/login/bottom_sheet_privacy_policy_and_toc.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/branding/branding.dart';

/// Common loading widgets that can be reused across all company loading screens
class CommonLoadingWidgets {
  /// Builds the main loading screen logo section with company branding
  static Widget buildLoadingLogoSection(BuildContext context, Animation<double> animation) {
    final widthforImg = MediaQuery.of(context).size.width * 0.4;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center, 
      children: [
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.30,
          child: Image.asset(
            splashScreenLoadingScreenCompanyLogo2,
            fit: BoxFit.contain,
          ),
        ),
        FadeTransition(
          opacity: animation,
          child: SizedBox(
            width: widthforImg,
            child: Image.asset(
              splashScreenLoadingScreenCompanyLogo3,
              fit: BoxFit.contain,
            ),
          )
        )
      ]
    );
  }

  /// Builds the connectivity listener for handling internet connection changes
  static Widget buildConnectivityListener({
    required BuildContext context,
    required Widget child,
  }) {
    return BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
      listener: (context, state) {
        if (state is InternetConnectivityFailure) {
          // Show the no internet connection modal here
          WidgetsBinding.instance.addPostFrameCallback((_) {
            getBotttomNoInternetConnection(
              heading: noInternetConnectionText["text4"]!,
              context: context,
            ).then((_) {
              // Once the bottom sheet is dismissed, reset the notifier
              isBottomSheetOpenNotifier.value = false;
            });
          });
        } else if (isBottomSheetOpenNotifier.value == true &&
            state is InternetConnectivitySuccess) {
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            isBottomSheetOpenNotifier.value = false;
            Navigator.of(context).pop();
          });
        }
      },
      child: BlocBuilder<InternetConnectivityBloc, InternetConnectivityState>(
        builder: (context, state) {
          return ColoredBox(
            color: Colors.white,
            child: Scaffold(
              body: child,
            ),
          );
        },
      ),
    );
  }

  /// Helper method to get privacy policy response
  static Future<http.Response> getPrivacyPolicyResponse() async {
    return await BackendApi.initiateGetCall(
      ApiUrls.setting,
      params: {
        "settingName": "PRIVACY_POLICY",
        "organisationId": organisationId
      },
    );
  }

  /// Helper method to get terms and conditions response
  static Future<http.Response> getTermsAndConditionsResponse() async {
    return await BackendApi.initiateGetCall(
      ApiUrls.setting,
      params: {
        "settingName": "TERMS_AND_CONDITION",
        "organisationId": organisationId
      },
    );
  }

  /// Helper method to check if any rider is connected
  static bool isAnyRiderConnected(BuildContext context) {
    List<Rider> riders = context.read<UserVehicleBloc>().state.riders;
    return riders.any((element) => element.isConnected);
  }

  /// Helper method to parse settings from responses
  static Map<String, UserActivitySetting> parseSettings(
    http.Response privacyPolicyResponse,
    http.Response termsConditionsResponse
  ) {
    JsonDecoder decoder = const JsonDecoder();
    
    // Parse privacy policy
    Map<String, dynamic> privacyPolicyDetails =
        decoder.convert(privacyPolicyResponse.body);
    Setting privacyPolicy = Setting.fromJson(privacyPolicyDetails);
    
    // Parse terms and conditions
    Map<String, dynamic> termsConditionsResponseDetails =
        decoder.convert(termsConditionsResponse.body);
    Setting termsAndConditions =
        Setting.fromJson(termsConditionsResponseDetails);
    
    // Create settings
    UserActivitySetting privacyPolicySetting = UserActivitySetting(
      activityType: ActivityType.privacyPolicyAcceptance,
      isNewSettingExist: false,
      message: privacyPolicy.message,
      value: privacyPolicy.value
    );
    
    UserActivitySetting termsAndConditionsSetting = UserActivitySetting(
      activityType: ActivityType.termsConditionsAcceptance,
      isNewSettingExist: false,
      message: termsAndConditions.message,
      value: termsAndConditions.value
    );
    
    return {
      'privacyPolicy': privacyPolicySetting,
      'termsAndConditions': termsAndConditionsSetting
    };
  }
}
