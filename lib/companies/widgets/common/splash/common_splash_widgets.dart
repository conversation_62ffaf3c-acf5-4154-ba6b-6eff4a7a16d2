import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/branding/branding.dart';

/// Common splash widgets that can be reused across all company splash screens
class CommonSplashWidgets {
  /// Builds the animated splash logo section with company branding
  static Widget buildAnimatedLogoSection({
    required BuildContext context,
    required AnimationController controller,
    required Animation<Offset> offsetAnimation,
  }) {
    final widthforImg = MediaQuery.of(context).size.width * 0.3;
    
    return Center(
      child: SlideTransition(
        position: offsetAnimation,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Image.asset(
              splashScreenLoadingPageCompanyLogoBg,
              width: widthforImg,
              fit: BoxFit.contain,
            ),
            AnimatedBuilder(
              animation: controller,
              child: Image.asset(
                splashScreenLoadingPageCircularWhite,
                width: widthforImg * 0.85,
                fit: BoxFit.contain,
              ),
              builder: (BuildContext context, Widget? child) {
                return Transform.rotate(
                  angle: controller.value * 4.0 * math.pi,
                  child: child,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the connectivity listener for handling internet connection changes
  static Widget buildConnectivityListener({
    required BuildContext context,
    required Widget child,
    required AnimationController controller,
    required AnimationController controllerTwo,
    required VoidCallback onNavigateToLoading,
  }) {
    return BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
      listener: (context, state) async {
        if (state is InternetConnectivityFailure) {
          // Show the no internet connection modal here
          WidgetsBinding.instance.addPostFrameCallback((_) {
            controller.dispose();
            controllerTwo.dispose();
            getBotttomNoInternetConnection(
              heading: noInternetConnectionText["text4"]!,
              context: context,
            ).then((_) {
              // Once the bottom sheet is dismissed, reset the notifier
              isBottomSheetOpenNotifier.value = false;
            });
          });
        } else if (isBottomSheetOpenNotifier.value == true &&
            state is InternetConnectivitySuccess) {
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            isBottomSheetOpenNotifier.value = false;
            Navigator.of(context).pop();
            SnackBarMessage.message(noInternetConnectionText["text5"]!,
                backOnlineColorGreen, context);
            onNavigateToLoading();
          });
        }
      },
      child: child,
    );
  }

  /// Helper method to create slide animation
  static Animation<Offset> createSlideAnimation(AnimationController controller) {
    return Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(-0.5, 0.0),
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.elasticIn,
    ));
  }

  /// Helper method for display size calculations
  static Size displaySize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// Helper method for display height calculations
  static double displayHeight(BuildContext context) {
    return displaySize(context).height;
  }

  /// Helper method for display width calculations
  static double displayWidth(BuildContext context) {
    return displaySize(context).width;
  }
}
