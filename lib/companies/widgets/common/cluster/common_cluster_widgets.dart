import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/widgets/cluster/body.dart';
import 'package:nds_app/widgets/cluster/bottom_menu.dart';
import 'package:nds_app/widgets/cluster/title_row.dart';

/// Common cluster widgets that can be reused across all company cluster screens
class CommonClusterWidgets {
  /// Builds the main cluster layout with title, body, and bottom menu
  static Widget buildClusterLayout({
    required BuildContext context,
    required VehicleInfo vehicleInfo,
    required String userName,
  }) {
    Dimensions dimensions = Dimensions(context);
    bool isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
    
    return Scaffold(
      backgroundColor: colorGrey900,
      body: SafeArea(
        child: Column(
          children: [
            buildTitleSection(
              dimensions: dimensions,
              isPortrait: isPortrait,
              userName: userName,
            ),
            buildBodySection(
              isPortrait: isPortrait,
              vehicleInfo: vehicleInfo,
            ),
            buildBottomMenuSection(
              isPortrait: isPortrait,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the title section with padding
  static Widget buildTitleSection({
    required Dimensions dimensions,
    required bool isPortrait,
    required String userName,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: isPortrait
            ? 20
            : 5 / 360 * (isPortrait ? dimensions.width : dimensions.height),
        vertical: isPortrait
            ? 10
            : 5 / 360 * (isPortrait ? dimensions.width : dimensions.height),
      ),
      child: ClusterTitleRow(
        isPortrait: isPortrait,
        userName: userName,
      ),
    );
  }

  /// Builds the expanded body section
  static Widget buildBodySection({
    required bool isPortrait,
    required VehicleInfo vehicleInfo,
  }) {
    return Expanded(
      child: ClusterBody(
        isPortrait: isPortrait,
        vehicleInfo: vehicleInfo,
      ),
    );
  }

  /// Builds the bottom menu section
  static Widget buildBottomMenuSection({
    required bool isPortrait,
  }) {
    return ClusterBottomMenu(
      isPortrait: isPortrait,
    );
  }
}
