import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/widgets/connect/connect_view_toggle_button.dart';

/// Common connect vehicle widgets that can be reused across all company connect vehicle screens
class CommonConnectVehicleWidgets {
  /// Builds the header section with back button and title
  static Widget buildHeaderSection({
    required BuildContext context,
    required double width,
    required VoidCallback onBackPressed,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          onTap: onBackPressed,
          child: Icon(
            Icons.arrow_back_ios,
            color: Theme.of(context).primaryColor,
          ),
        ),
        Text(
          connectVehicleText["text1"]!,
          style: urbanistTextStyle(
            20 / 414 * width,
            Theme.of(context).primaryColor,
            FontWeight.w700,
          ),
        ),
        const SizedBox(width: 24),
      ],
    );
  }

  /// Builds the toggle button section
  static Widget buildToggleSection({
    required Color color,
    required ColorType colorType,
  }) {
    return ConnectViewToggleButton(
      color: color,
      colorType: colorType,
    );
  }

  /// Builds the QR scanner container
  static Widget buildQrScannerContainer({
    required BuildContext context,
    required double height,
    required double width,
    required Widget qrView,
    required bool isFlashOn,
    required VoidCallback onFlashToggle,
  }) {
    return Stack(
      children: [
        Align(
          alignment: Alignment.center,
          child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(8 / 414 * width),
            ),
            height: 284 / 896 * height,
            width: 284 / 414 * width,
            child: qrView,
          ),
        ),
        Positioned(
          bottom: -20,
          left: 0,
          right: 0,
          child: Center(
            child: GestureDetector(
              onTap: onFlashToggle,
              child: Container(
                height: 60 / 896 * height,
                width: 60 / 414 * width,
                decoration: BoxDecoration(
                  color: isFlashOn ? colorWhite : colorGrey800,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  Icons.flash_on,
                  color: isFlashOn ? colorGrey800 : colorWhite,
                  size: 30 / 414 * width,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the chassis code input section
  static Widget buildChassisCodeSection({
    required BuildContext context,
    required double height,
    required double width,
    required Widget codeInputWidget,
    required String? errorMessage,
  }) {
    return Column(
      children: [
        SizedBox(height: 40 / 896 * height),
        Text(
          connectVehicleText["text2"]!,
          style: urbanistTextStyle(
            16 / 414 * width,
            colorGrey600,
            FontWeight.w500,
          ),
        ),
        SizedBox(height: 40 / 896 * height),
        Container(
          height: 284 / 896 * height,
          width: 284 / 414 * width,
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(8 / 414 * width),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(height: 20 / 896 * height),
              Image.asset(
                connectVehicleImages["password"]!,
                height: 120 / 896 * height,
                width: 120 / 414 * width,
              ),
              SizedBox(height: 20 / 896 * height),
              Text(
                connectVehicleText["text4"]!,
                style: urbanistTextStyle(
                  16 / 414 * width,
                  colorGrey600,
                  FontWeight.w500,
                ),
              ),
              SizedBox(height: 20 / 896 * height),
              codeInputWidget,
              SizedBox(height: 20 / 896 * height),
            ],
          ),
        ),
        SizedBox(height: 20 / 896 * height),
        if (errorMessage != null && errorMessage.isNotEmpty)
          Text(
            errorMessage,
            style: urbanistTextStyle(
              16 / 414 * width,
              colorRed,
              FontWeight.w500,
            ),
          ),
      ],
    );
  }

  /// Builds a simple code input row for chassis code entry
  static Widget buildSimpleCodeInput({
    required List<TextEditingController> controllers,
    required List<FocusNode> focusNodes,
    required Function(String, int) onChanged,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(
        4,
        (index) => Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            border: Border.all(color: colorGrey600),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            controller: controllers[index],
            focusNode: focusNodes[index],
            textAlign: TextAlign.center,
            maxLength: 1,
            onChanged: (value) => onChanged(value, index),
            decoration: const InputDecoration(
              border: InputBorder.none,
              counterText: '',
            ),
          ),
        ),
      ),
    );
  }
}
