import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nds_app/blocs/login/login_bloc.dart';
import 'package:nds_app/blocs/login/login_events.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/models/enums/mobile_number_status.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/widgets/login/privacy_policy_and_toc_check_box.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/utils/toast.dart';

/// Common login widgets that can be reused across all company login screens
class CommonLoginWidgets {
  static Widget buildLoginImageSection() {
    return Expanded(
      child: Container(
        alignment: Alignment.center,
        child: SizedBox(
          child: Image.asset(
            "assets/login/loginScreenScooter.png",
          ),
        ),
      ),
    );
  }

  static Widget buildWelcomeSection(Dimensions dimensions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          loginScreen["text15"]!,
          style: urbanistTextStyle(
              0.026 * dimensions.height, loginThemeColor, FontWeight.w600),
        ),
        SizedBox(
          height: 0.01 * dimensions.height,
        ),
        Text(
          loginScreen["text16"]!,
          style: urbanistTextStyle(
              0.015 * dimensions.height, loginThemeColor, FontWeight.w400),
        ),
      ],
    );
  }

  static Widget buildMobileNumberSection({
    required Dimensions dimensions,
    required LoginScreenBloc bloc,
    required TextEditingController mobileNumberController,
    required Function(String) fillNumber,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          loginScreen["text2"]!,
          style: urbanistTextStyle(
              0.016 * dimensions.height, primaryBlueLight, FontWeight.w500),
        ),
        SizedBox(
          height: 0.0096 * dimensions.height,
        ),
        StreamBuilder<MobileNumberStatus>(
            stream: bloc.mobileNumberStatus,
            initialData: MobileNumberStatus.notEntered,
            builder: (context, snapshot) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            color: unSelectedTextBoxColor,
                            borderRadius:
                                const BorderRadius.all(Radius.circular(8.0)),
                            border: Border.all(
                                color: (snapshot.data ==
                                        MobileNumberStatus.notEntered
                                    ? colorBlack800
                                    : (snapshot.data == MobileNumberStatus.valid
                                        ? secondaryBlue
                                        : colorRed)))),
                        child: TextField(
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp('[0-9]'))
                          ],
                          style: TextStyle(color: loginThemeColor),
                          controller: mobileNumberController,
                          onChanged: fillNumber,
                          decoration: InputDecoration(
                              prefixIcon: Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      '+91',
                                      style: urbanistTextStyle(
                                          0.019 * dimensions.height,
                                          loginThemeColor,
                                          FontWeight.w400),
                                    ),
                                    const SizedBox(width: 12.0),
                                    Container(
                                      height: 0.5 * (kToolbarHeight),
                                      width: 1.5,
                                      color: loginThemeColor,
                                    ),
                                  ],
                                ),
                              ),
                              counterText: "",
                              labelText: loginScreen["text3"]!,
                              labelStyle: urbanistTextStyle(
                                  0.019 * dimensions.height,
                                  loginThemeColor,
                                  FontWeight.w500),
                              floatingLabelBehavior:
                                  FloatingLabelBehavior.never,
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.fromLTRB(
                                  0.048 * dimensions.width,
                                  0.0,
                                  0.0,
                                  0.0217 * dimensions.height)),
                          maxLength: 10,
                          keyboardType: const TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                        ),
                      ),
                      Visibility(
                        visible: snapshot.data == MobileNumberStatus.invalid,
                        child: Positioned(
                          top: 0.023 * dimensions.height,
                          right: 0.023 * dimensions.width,
                          child: Image.asset(
                            loginScreenImages["exclamationMark"]!,
                            height: 0.025 * dimensions.height,
                          ),
                        ),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 0.00488 * dimensions.height,
                  ),
                  Visibility(
                    visible: snapshot.data == MobileNumberStatus.invalid,
                    child: Text(
                      loginScreen["text6"]!,
                      style: urbanistTextStyle(
                          0.014 * dimensions.height, colorRed, FontWeight.w500),
                    ),
                  ),
                ],
              );
            }),
      ],
    );
  }

  static Widget buildPrivacyPolicySection(List<UserActivitySetting> settings) {
    return CheckBoxPrivacyPolicyAndToc(
      settings: settings,
      activities: const [
        ActivityType.privacyPolicyAcceptance,
        ActivityType.termsConditionsAcceptance
      ],
    );
  }

  static Widget buildLoginButton({
    required LoginScreenBloc bloc,
    required bool isPrivacyPolicyAndTocChecked,
    required TextEditingController mobileNumberController,
    required List<UserActivitySetting> settings,
    required BuildContext context,
    required Function(String phoneNumber, String phoneNumText,
            List<UserActivitySetting> settings)
        onNavigateToOtp,
  }) {
    return StreamBuilder(
      stream: bloc.mobileNumberStatus,
      builder: (context, snapshot) {
        return snapshot.data == MobileNumberStatus.valid &&
                isPrivacyPolicyAndTocChecked
            ? GestureDetector(
                onTap: () async {
                  getCircularProgressIndicator(context);
                  String phoneNumber =
                      "%2B91${mobileNumberController.text.trim()}";
                  String? error = await LoginService.sendLoginOtp(phoneNumber);

                  // ignore: use_build_context_synchronously
                  Navigator.pop(context);
                  if (error == null) {
                    onNavigateToOtp(
                      phoneNumber,
                      mobileNumberController.text.trim(),
                      settings,
                    );
                  } else {
                    CustomToast.message(error);
                    bloc.eventSink.add(LoginErrorEvent());
                  }
                },
                child: CustomButton.gesture(
                  text: loginScreen["text4"]!,
                  backgroundColor: loginThemeColor,
                  size: CustomButtonSize.fullWidth,
                  textColor: colorWhite,
                  fontWeight: FontWeight.w600,
                ))
            : CustomButton.gesture(
                text: loginScreen["text4"]!,
                backgroundColor: secondButtonColor,
                size: CustomButtonSize.fullWidth,
                textColor: colorWhite,
                fontWeight: FontWeight.w600,
              );
      },
    );
  }
}
