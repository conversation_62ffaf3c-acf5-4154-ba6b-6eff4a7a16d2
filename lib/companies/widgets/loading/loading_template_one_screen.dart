import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
//import 'package:location/location.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_event.dart';

import 'package:nds_app/common/constant.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/setting.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/companies/factoryFiles/dashboard_factory.dart';

import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/location_service.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/utils/check_alert.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/login/bottom_sheet_privacy_policy_and_toc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../factoryFiles/splash_screen_factory.dart';
import '../common/preference_helper.dart';
import '../common/loading/common_loading_widgets.dart';
import 'base_loading.dart';

/// Template One for loading screens
/// Contains all the loading functionality and UI
/// Used by all companies that share the same loading template
class LoadingTemplateOneScreen extends BaseLoading {
  const LoadingTemplateOneScreen({Key? key}) : super(key: key);

  @override
  LoadingTemplateOneScreenState createState() =>
      LoadingTemplateOneScreenState();
}

class LoadingTemplateOneScreenState extends State<LoadingTemplateOneScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool isNonPromotionalScreen = false;

  @override
  dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInCirc,
    );
    _controller.forward(
      from: 0,
    );
    _navigatetoLoadingScreen();
  }

  _navigatetoLoadingScreen() {
    Future.delayed(const Duration(milliseconds: 500), () async {
      // ignore: use_build_context_synchronously
      sharedPreferences = await SharedPreferences.getInstance();
      String? token = await LoginService.checkLoginStatus();
      final bool isOnboardingShown = await PreferenceHelper.isOnboardingDone();
      JsonDecoder decoder = const JsonDecoder();

      // ignore: use_build_context_synchronously
      final settingResults = await getAwaitedResults(context);

      final http.Response privacyPolicyResponse =
          settingResults[0] as http.Response;
      final termsConditionsResponse = settingResults[1] as http.Response;

      if (privacyPolicyResponse.statusCode == 200 &&
          termsConditionsResponse.statusCode == 200) {
        //Privacy Policy Get and Persist in Shared Preference
        Map<String, dynamic> privacyPolicyDetails =
            decoder.convert(privacyPolicyResponse.body);
        Setting privacyPolicy = Setting.fromJson(privacyPolicyDetails);

        //Terms And Conditions Get and Persist in Shared Preference
        Map<String, dynamic> termsConditionsResponseDetails =
            decoder.convert(termsConditionsResponse.body);
        Setting termsAndConditions =
            Setting.fromJson(termsConditionsResponseDetails);

        if (isInternetAvailable ?? true) {
          if (token != null) {
            http.Response privacyPolicyUpdateResponse =
                await BackendApi.initiateGetCall(
              ApiUrls.getUserKeyActivity,
              params: {
                "activityType":
                    ActivityType.privacyPolicyAcceptance.requestName,
                "organisationId": organisationId
              },
            );
            Map<String, dynamic> privacyPolicyUpdateDetails =
                decoder.convert(privacyPolicyUpdateResponse.body);
            UserActivitySetting privacyPolicyUpdate =
                UserActivitySetting.fromJson(privacyPolicyUpdateDetails);

            if (privacyPolicyUpdate.isNewSettingExist == true) {
              // ignore: use_build_context_synchronously
              await getBotttomSheetPrivacyPolicyAndToc(
                  heading: loginScreen['text12']!,
                  // ignore: use_build_context_synchronously
                  context: context,
                  userActivitySetting: privacyPolicyUpdate);
            }

            http.Response termsAndConditionsUpdateResponse =
                await BackendApi.initiateGetCall(
              ApiUrls.getUserKeyActivity,
              params: {
                "activityType":
                    ActivityType.termsConditionsAcceptance.requestName,
                "organisationId": organisationId
              },
            );
            Map<String, dynamic> termsAndConditionsUpdateDetails =
                decoder.convert(termsAndConditionsUpdateResponse.body);
            UserActivitySetting termAndConditionUpdate =
                UserActivitySetting.fromJson(termsAndConditionsUpdateDetails);

            if (termAndConditionUpdate.isNewSettingExist == true) {
              // ignore: use_build_context_synchronously
              await getBotttomSheetPrivacyPolicyAndToc(
                  heading: loginScreen['text13']!,
                  // ignore: use_build_context_synchronously
                  context: context,
                  userActivitySetting: termAndConditionUpdate);
            }
          }

          if (isBottomSheetOpenNotifier.value == false) {
            // ignore: use_build_context_synchronously
            List<Rider> rider = context.read<UserVehicleBloc>().state.riders;
            bool isConnected = rider.any((element) => element.isConnected);

            // ignore: use_build_context_synchronously
            // ignore: use_build_context_synchronously
            Navigator.pushAndRemoveUntil(context,
                MaterialPageRoute(builder: (context) {
              privacyPolicySetting = UserActivitySetting(
                  activityType: ActivityType.privacyPolicyAcceptance,
                  isNewSettingExist: false,
                  message: privacyPolicy.message,
                  value: privacyPolicy.value);

              termsAndConditionsSetting = UserActivitySetting(
                  activityType: ActivityType.termsConditionsAcceptance,
                  isNewSettingExist: false,
                  message: termsAndConditions.message,
                  value: termsAndConditions.value);
              return token == null
                  ? isOnboardingShown
                      ? SplashScreenFactory.createLoginScreen(
                          settings: [
                            privacyPolicySetting!,
                            termsAndConditionsSetting!
                          ],
                        )
                      : SplashScreenFactory.createOnboardingScreen()
                  : isConnected || !isB2CUser
                      ? DashboardFactory.createDashboard()
                      : SplashScreenFactory.createPromotionalScreen();
            }), (route) => false);
          }
        } else if (privacyPolicyResponse.statusCode != 200) {
          CustomToast.message(toastMessageText['text4']!);
        } else {
          CustomToast.message(toastMessageText['text4']!);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    checkAlert(context);
    currentContext = context;

    return CommonLoadingWidgets.buildConnectivityListener(
      context: context,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CommonLoadingWidgets.buildLoadingLogoSection(context, _animation),
        ],
      ),
    );
  }

  Future<http.Response> getPrivacyPolicyResponse() async {
    return CommonLoadingWidgets.getPrivacyPolicyResponse();
  }

  Future<http.Response> getTermsAndConditionsResponse() async {
    return CommonLoadingWidgets.getTermsAndConditionsResponse();
  }

  Future<bool> getLocation() async {
    try {
      userLocationData = userLocationData ?? await determineLocation();
    } catch (e) {
      debugPrint(e.toString());
    }
    return true;
  }

  Future<String> getUserVehicles(BuildContext context) async {
    context.read<UserVehicleBloc>().add(LoadUserVehicleEvent());
    return await Future.delayed(
        const Duration(milliseconds: 500), () => 'Completed');
  }

  Future<List<Object>> getAwaitedResults(BuildContext context) async {
    return await Future.wait([
      getPrivacyPolicyResponse(),
      getTermsAndConditionsResponse(),
      getLocation(),
      awaitFunction(),
      getUserVehicles(context),
    ]);
  }

  Future<Object> awaitFunction() async {
    return await Future.delayed(
        const Duration(seconds: 4, milliseconds: 500), () => 'Completed');
  }
}
