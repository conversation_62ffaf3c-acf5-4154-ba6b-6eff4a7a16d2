import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';

import 'package:nds_app/companies/factoryFiles/connect_vehicle_factory.dart';
import 'package:nds_app/widgets/dashboard/action_button_row.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

import '../../baseScreens/select_fleet/base_select_fleet.dart';

/// Template for select fleet screens
/// Contains all the select fleet functionality and UI
/// Used by all companies that share the same select fleet template
class SelectFleetTemplateOneScreen extends BaseSelectFleet {
  const SelectFleetTemplateOneScreen({super.key});

  static const List<String> fleets = [companyName];

  @override
  Widget build(BuildContext context) {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': "Select Fleet Template One",
      'screen_class': runtimeType.toString(),
    });

    Dimensions dimensions = Dimensions(context);

    return Scaffold(
      appBar: PreferredSize(
          preferredSize: Size.fromHeight(120 / 896 * dimensions.height),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              border: const Border(
                bottom: BorderSide(color: colorGrey400, width: 1.0),
              ),
            ),
            width: dimensions.width,
            height: 120 / 896 * dimensions.height,
            child: ActionButtonRow(
                isConnected: false,
                connectAction: () {},
                context: context,
                userName: "",
                isDashboardActionRow: false),
          )),
      body: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: 20 / 414 * dimensions.width,
            vertical: 24 / 896 * dimensions.height),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              selectFleetText["title"]!,
              style: Theme.of(context).textTheme.headlineLarge,
            ),
            SizedBox(
              height: 28 / 896 * dimensions.height,
            ),
            SizedBox(
                height: 610 / 896 * dimensions.height,
                child: getFleets(fleets, dimensions, context)),
            const Expanded(child: SizedBox()),
            Align(
              alignment: Alignment.bottomRight,
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            ConnectVehicleFactory.createConnectVehicle()),
                  );
                },
                child: Container(
                  width: 60 / 414 * dimensions.width,
                  height: 60 / 414 * dimensions.width,
                  decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(30)),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Theme.of(context).scaffoldBackgroundColor,
                    size: 24 / 414 * dimensions.width,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget getFleets(
      List<String> fleets, Dimensions dimensions, BuildContext context) {
    return ListView.builder(
      itemCount: fleets.length,
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.only(bottom: 16 / 896 * dimensions.height),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) =>
                        ConnectVehicleFactory.createConnectVehicle()),
              );
            },
            child: Container(
              height: 81 / 896 * dimensions.height,
              width: 374 / 414 * dimensions.width,
              padding: EdgeInsets.all(16 / 414 * dimensions.width),
              decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  borderRadius:
                      BorderRadius.circular(8 / 414 * dimensions.width),
                  border: Border.all(
                    color: Theme.of(context).highlightColor.withOpacity(0.25),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                        color: Theme.of(context).shadowColor.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: const Offset(2, 2))
                  ]),
              child: Row(
                children: [
                  Container(
                    width: 49 / 414 * dimensions.width,
                    height: 49 / 414 * dimensions.width,
                    decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(8)),
                    child: Icon(
                      Icons.business,
                      color: Theme.of(context).scaffoldBackgroundColor,
                      size: 24 / 414 * dimensions.width,
                    ),
                  ),
                  SizedBox(
                    width: 16 / 414 * dimensions.width,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          fleets[index].toUpperCase(),
                          style: Theme.of(context).textTheme.headlineMedium,
                        ),
                        Text(
                          selectFleetText["subtitle"]!,
                          style: Theme.of(context).textTheme.labelMedium,
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Theme.of(context).primaryColor,
                    size: 16 / 414 * dimensions.width,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
