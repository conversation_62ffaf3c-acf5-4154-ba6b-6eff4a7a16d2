import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';

import 'b2c/views/vehicle/vehicleHealth/vehicle_health_page_b2c.dart';
import 'lapa/views/vehicle/vehicleHealth/vehicle_health_page_lapa.dart';
import 'nds/views/vehicle/vehicleHealth/vehicle_health_page_nds.dart';
import 'prodred/views/vehicle/vehicleHealth/vehicle_health_page_prodred.dart';
import 'nichesolv/views/vehicle/vehicleHealth/vehicle_health_page_nichesolv.dart';

class VehicleHealthPageFactory {
  static Widget createVehicleHealthPage({
    required String imei,
    required void Function() onBackPressed,
    required void Function(String partType, String partLabel) onHealthDetailPressed,
    required Vehicle vehicle,
  }) {
    switch (companyName) {
      case 'b2c':
        return VehicleHealthPageB2C(
          imei: imei,
          onBackPressed: onBackPressed,
          onHealthDetailPressed: onHealthDetailPressed,
          vehicle: vehicle,
        );
      case 'lapa':
        return VehicleHealthPageLapa(
          imei: imei,
          onBackPressed: onBackPressed,
          onHealthDetailPressed: onHealthDetailPressed,
          vehicle: vehicle,
        );
      case 'nds':
        return VehicleHealthPageNDS(
          imei: imei,
          onBackPressed: onBackPressed,
          onHealthDetailPressed: onHealthDetailPressed,
          vehicle: vehicle,
        );
      case 'prodred':
        return VehicleHealthPageProdRed(
          imei: imei,
          onBackPressed: onBackPressed,
          onHealthDetailPressed: onHealthDetailPressed,
          vehicle: vehicle,
        );
      case 'nichesolv':
        return VehicleHealthPageNichesolv(
          imei: imei,
          onBackPressed: onBackPressed,
          onHealthDetailPressed: onHealthDetailPressed,
          vehicle: vehicle,
        );
      default:
        return VehicleHealthPageB2C(
          imei: imei,
          onBackPressed: onBackPressed,
          onHealthDetailPressed: onHealthDetailPressed,
          vehicle: vehicle,
        );
    }
  }
}
