import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/trip.dart';
import 'package:nds_app/companies/widgets/vehicle/base_route_screen.dart';
import 'package:nds_app/companies/widgets/vehicle/route_screen_template_one_screen.dart';

/// Factory class to create company-specific route screens based on the current company configuration
class RouteScreenFactory {
  /// Creates and returns the appropriate route widget based on the company name
  static Widget createRouteScreen({
    required Trip trip,
  }) {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getRouteScreenTemplate(trip: trip);
  }

  /// Returns the appropriate route template based on company validation requirements
  static BaseRouteScreen _getRouteScreenTemplate({
    required Trip trip,
  }) {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return RouteScreenTemplateOneScreen(trip: trip);
    }
  }

  /// Returns the company-specific route class name for debugging purposes
  static String getRouteScreenClassName() {
    return 'RouteScreenTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
