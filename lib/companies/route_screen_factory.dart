import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/trip.dart';

import 'b2c/views/vehicle/route_screen_b2c.dart';
import 'lapa/views/vehicle/route_screen_lapa.dart';
import 'nds/views/vehicle/route_screen_nds.dart';
import 'prodred/views/vehicle/route_screen_prodred.dart';
import 'nichesolv/views/vehicle/route_screen_nichesolv.dart';

class RouteScreenFactory {
  static Widget createRouteScreen({
    required Trip trip,
  }) {
    switch (companyName) {
      case 'b2c':
        return RouteScreenB2C(trip: trip);
      case 'lapa':
        return RouteScreenLapa(trip: trip);
      case 'nds':
        return RouteScreenNDS(trip: trip);
      case 'prodred':
        return RouteScreenProdRed(trip: trip);
      case 'nichesolv':
        return RouteScreenNichesolv(trip: trip);
      default:
        return RouteScreenB2C(trip: trip);
    }
  }
}
