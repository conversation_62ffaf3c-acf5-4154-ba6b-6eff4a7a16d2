import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';

import 'b2c/views/profile/setting_b2c.dart';
import 'lapa/views/profile/setting_lapa.dart';
import 'nds/views/profile/setting_nds.dart';
import 'prodred/views/profile/setting_prodred.dart';
import 'nichesolv/views/profile/setting_nichesolv.dart';

class SettingFactory {
  static Widget createSetting({
    required void Function() onBackPressed,
  }) {
    switch (companyName) {
      case 'b2c':
        return SettingB2C(onBackPressed: onBackPressed);
      case 'lapa':
        return SettingLapa(onBackPressed: onBackPressed);
      case 'nds':
        return SettingNDS(onBackPressed: onBackPressed);
      case 'prodred':
        return SettingProdRed(onBackPressed: onBackPressed);
      case 'nichesolv':
        return SettingNichesolv(onBackPressed: onBackPressed);
      default:
        return SettingB2C(onBackPressed: onBackPressed);
    }
  }
}
