import 'package:flutter/material.dart';
import 'package:nds_app/widgets/dashboard/action_button_row.dart';

/// Factory class for ActionButtonRow - now uses the reusable component
/// All companies use the same ActionButtonRow implementation
class ActionButtonRowFactory {
  static Widget createActionButtonRow({
    required bool isConnected,
    required Function connectAction,
    required BuildContext context,
    required String userName,
    required bool isDashboardActionRow,
  }) {
    // All companies now use the same reusable ActionButtonRow component
    return ActionButtonRow(
      isConnected: isConnected,
      connectAction: connectAction,
      context: context,
      userName: userName,
      isDashboardActionRow: isDashboardActionRow,
    );
  }
}
