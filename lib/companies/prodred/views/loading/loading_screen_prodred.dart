import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
//import 'package:location/location.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_event.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/common/constant.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/setting.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/companies/dashboard_factory.dart';

import 'package:nds_app/screens/splashScreen/splash_screen.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/location_service.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/utils/check_alert.dart';
import 'package:nds_app/utils/toast.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import 'package:nds_app/widgets/login/bottom_sheet_privacy_policy_and_toc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../branding/branding.dart';
import '../../../../companies/splash_screen_factory.dart';

/// ProdRed-specific loading screen
/// Uses ProdRed branding and can be customized for ProdRed-specific behavior
class LoadingScreenProdRed extends StatefulWidget {
  const LoadingScreenProdRed({Key? key}) : super(key: key);

  @override
  LoadingScreenProdRedState createState() => LoadingScreenProdRedState();
}

class LoadingScreenProdRedState extends State<LoadingScreenProdRed>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool isNonPromotionalScreen = false;

  @override
  dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInCirc,
    );
    _controller.forward(
      from: 0,
    );
    _navigatetoLoadingScreen();
  }

  _navigatetoLoadingScreen() {
    Future.delayed(const Duration(milliseconds: 500), () async {
      // ignore: use_build_context_synchronously
      sharedPreferences = await SharedPreferences.getInstance();
      String? token = await LoginService.checkLoginStatus();
      final bool isOnboardingShown = await PreferenceHelper.isOnboardingDone();
      JsonDecoder decoder = const JsonDecoder();

      // ignore: use_build_context_synchronously
      final settingResults = await getAwaitedResults(context);

      final http.Response privacyPolicyResponse =
          settingResults[0] as http.Response;
      final termsConditionsResponse = settingResults[1] as http.Response;

      if (privacyPolicyResponse.statusCode == 200 &&
          termsConditionsResponse.statusCode == 200) {
        //Privacy Policy Get and Persist in Shared Preference
        Map<String, dynamic> privacyPolicyDetails =
            decoder.convert(privacyPolicyResponse.body);
        Setting privacyPolicy = Setting.fromJson(privacyPolicyDetails);

        //Terms And Conditions Get and Persist in Shared Preference
        Map<String, dynamic> termsConditionsResponseDetails =
            decoder.convert(termsConditionsResponse.body);
        Setting termsAndConditions =
            Setting.fromJson(termsConditionsResponseDetails);

        if (isInternetAvailable ?? true) {
          if (token != null) {
            http.Response privacyPolicyUpdateResponse =
                await BackendApi.initiateGetCall(
              ApiUrls.getUserKeyActivity,
              params: {
                "activityType":
                    ActivityType.privacyPolicyAcceptance.requestName,
                "organisationId": organisationId
              },
            );
            Map<String, dynamic> privacyPolicyUpdateDetails =
                decoder.convert(privacyPolicyUpdateResponse.body);
            UserActivitySetting privacyPolicyUpdate =
                UserActivitySetting.fromJson(privacyPolicyUpdateDetails);

            if (privacyPolicyUpdate.isNewSettingExist == true) {
              // ignore: use_build_context_synchronously
              await getBotttomSheetPrivacyPolicyAndToc(
                  heading: loginScreen['text12']!,
                  // ignore: use_build_context_synchronously
                  context: context,
                  userActivitySetting: privacyPolicyUpdate);
            }

            http.Response termsAndConditionsUpdateResponse =
                await BackendApi.initiateGetCall(
              ApiUrls.getUserKeyActivity,
              params: {
                "activityType":
                    ActivityType.termsConditionsAcceptance.requestName,
                "organisationId": organisationId
              },
            );
            Map<String, dynamic> termsAndConditionsUpdateDetails =
                decoder.convert(termsAndConditionsUpdateResponse.body);
            UserActivitySetting termAndConditionUpdate =
                UserActivitySetting.fromJson(termsAndConditionsUpdateDetails);

            if (termAndConditionUpdate.isNewSettingExist == true) {
              // ignore: use_build_context_synchronously
              await getBotttomSheetPrivacyPolicyAndToc(
                  heading: loginScreen['text13']!,
                  // ignore: use_build_context_synchronously
                  context: context,
                  userActivitySetting: termAndConditionUpdate);
            }
          }

          if (isBottomSheetOpenNotifier.value == false) {
            // ignore: use_build_context_synchronously
            List<Rider> rider = context.read<UserVehicleBloc>().state.riders;
            bool isConnected = rider.any((element) => element.isConnected);

            // ignore: use_build_context_synchronously
            // ignore: use_build_context_synchronously
            Navigator.pushAndRemoveUntil(context,
                MaterialPageRoute(builder: (context) {
              privacyPolicySetting = UserActivitySetting(
                  activityType: ActivityType.privacyPolicyAcceptance,
                  isNewSettingExist: false,
                  message: privacyPolicy.message,
                  value: privacyPolicy.value);

              termsAndConditionsSetting = UserActivitySetting(
                  activityType: ActivityType.termsConditionsAcceptance,
                  isNewSettingExist: false,
                  message: termsAndConditions.message,
                  value: termsAndConditions.value);
              return token == null
                  ? isOnboardingShown
                      ? SplashScreenFactory.createLoginScreen(
                          settings: [
                            privacyPolicySetting!,
                            termsAndConditionsSetting!
                          ],
                        )
                      : SplashScreenFactory.createOnboardingScreen()
                  : isConnected || !isB2CUser
                      ? DashboardFactory.createDashboard()
                      : SplashScreenFactory.createPromotionalScreen();
            }), (route) => false);
          }
        } else if (privacyPolicyResponse.statusCode != 200) {
          CustomToast.message(toastMessageText['text4']!);
        } else {
          CustomToast.message(toastMessageText['text4']!);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final widthforImg = displayWidth(context) * 0.4;
    checkAlert(context);
    currentContext = context;
    return BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
      listener: (context, state) {
        if (state is InternetConnectivityFailure) {
          // Show the no internet connection modal here
          WidgetsBinding.instance.addPostFrameCallback((_) {
            getBotttomNoInternetConnection(
              heading: noInternetConnectionText["text4"]!,
              context: context,
            ).then((_) {
              // Once the bottom sheet is dismissed, reset the notifier
              isBottomSheetOpenNotifier.value = false;
            });
          });
        } else if (isBottomSheetOpenNotifier.value == true &&
            state is InternetConnectivitySuccess) {
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            isBottomSheetOpenNotifier.value = false;
            Navigator.of(context).pop();

            await Navigator.pushReplacement(
              // ignore: use_build_context_synchronously
              context,
              MaterialPageRoute(
                  builder: (context) => const LoadingScreenProdRed()),
            );
          });
        }
      },
      child: BlocBuilder<InternetConnectivityBloc, InternetConnectivityState>(
        builder: (context, state) {
          return ColoredBox(
            color: Colors.white,
            child: Scaffold(
                body: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  SizedBox(
                    width: displayWidth(context) * 0.30,
                    child: Image.asset(
                      splashScreenLoadingScreenCompanyLogo2,
                      fit: BoxFit.contain,
                    ),
                  ),
                  FadeTransition(
                      opacity: _animation,
                      child: SizedBox(
                        width: widthforImg,
                        child: Image.asset(
                          splashScreenLoadingScreenCompanyLogo3,
                          fit: BoxFit.contain,
                        ),
                      ))
                ])
              ],
            )),
          );
        },
      ),
    );
  }

  Future<http.Response> getPrivacyPolicyResponse() async {
    return await BackendApi.initiateGetCall(
      ApiUrls.setting,
      params: {
        "settingName": "PRIVACY_POLICY",
        "organisationId": organisationId
      },
    );
  }

  Future<http.Response> getTermsAndConditionsResponse() async {
    return await BackendApi.initiateGetCall(
      ApiUrls.setting,
      params: {
        "settingName": "TERMS_AND_CONDITION",
        "organisationId": organisationId
      },
    );
  }

  Future<bool> getLocation() async {
    try {
      userLocationData = userLocationData ?? await determineLocation();
    } catch (e) {
      debugPrint(e.toString());
    }
    return true;
  }

  Future<String> getUserVehicles(BuildContext context) async {
    context.read<UserVehicleBloc>().add(LoadUserVehicleEvent());
    return await Future.delayed(
        const Duration(milliseconds: 500), () => 'Completed');
  }

  Future<List<Object>> getAwaitedResults(BuildContext context) async {
    return await Future.wait([
      getPrivacyPolicyResponse(),
      getTermsAndConditionsResponse(),
      getLocation(),
      awaitFunction(),
      getUserVehicles(context),
    ]);
  }

  Future<Object> awaitFunction() async {
    return await Future.delayed(
        const Duration(seconds: 4, milliseconds: 500), () => 'Completed');
  }
}

class PreferenceHelper {
  static Future<void> setOnboardingDone() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('onboarding_done', true);
  }

  static Future<bool> isOnboardingDone() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('onboarding_done') ?? false;
  }
}
