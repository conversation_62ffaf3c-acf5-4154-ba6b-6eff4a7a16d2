import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_bloc.dart';
import 'package:nds_app/blocs/connectivity/connectivity_failure.dart';
import 'package:nds_app/blocs/connectivity/connectivity_success.dart';
import 'package:nds_app/blocs/connectivity/connectivty_state.dart';
import 'package:nds_app/blocs/login/login_bloc.dart';
import 'package:nds_app/blocs/login/login_events.dart';
import 'package:nds_app/blocs/navigation/navigation_bar_stream.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/streams/privacy_policy_and_terms_check_box_data.dart';
import 'package:nds_app/utils/snake_bar.dart';
import 'package:nds_app/widgets/common/offline_screen.dart';
import '../../../widgets/common/login/common_login_widgets.dart';
import '../../../splash_screen_factory.dart';
import '../otp/otp_verification_screen_prodred.dart';

/// ProdRed-specific login screen
/// Uses common login widgets with ProdRed-specific navigation
class LoginScreenProdRed extends StatefulWidget {
  final List<UserActivitySetting> settings;

  const LoginScreenProdRed({
    Key? key,
    required this.settings,
  }) : super(key: key);

  @override
  State<LoginScreenProdRed> createState() => _LoginScreenProdRedState();
}

class _LoginScreenProdRedState extends State<LoginScreenProdRed> {
  late Dimensions dimensions;
  final _bloc = LoginScreenBloc();
  final _mobileNumberController = TextEditingController();
  late List<UserActivitySetting> settings;
  NavigationBarStream navigationBarStream = NavigationBarStream();

  CheckBoxPrivacyPolicyAndTosStream stream =
      CheckBoxPrivacyPolicyAndTosStream();

  bool isPrivacyPolicyAndTocChecked = false;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'ProdRed Login Screen',
      'screen_class': widget.runtimeType.toString(),
    });

    settings = widget.settings;
    stream.checkBox.listen((event) {
      isPrivacyPolicyAndTocChecked = event;
      _bloc.eventSink
          .add(EnterNumberEvent(mobileNumber: _mobileNumberController.text));
    });
    navigationBarStream.submitIndex(0);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    dimensions = Dimensions(context);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: BlocListener<InternetConnectivityBloc, InternetConnectivityState>(
        listener: (context, state) async {
          if (state is InternetConnectivityFailure) {
            // Show the no internet connection modal here
            WidgetsBinding.instance.addPostFrameCallback((_) {
              getBotttomNoInternetConnection(
                heading: noInternetConnectionText["text4"]!,
                context: context,
              ).then((_) {
                // Once the bottom sheet is dismissed, reset the notifier
                isBottomSheetOpenNotifier.value = false;
              });
            });
          } else if (isBottomSheetOpenNotifier.value == true &&
              state is InternetConnectivitySuccess) {
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              isBottomSheetOpenNotifier.value = false;
              Navigator.of(context).pop();
              SnackBarMessage.message(noInternetConnectionText["text5"]!,
                  backOnlineColorGreen, context);
              await Navigator.pushReplacement(
                // ignore: use_build_context_synchronously
                context,
                MaterialPageRoute(
                    builder: (context) =>
                        SplashScreenFactory.createLoadingScreen()),
              );
            });
          }
        },
        child: Padding(
          padding: EdgeInsets.fromLTRB(
              0.057 * dimensions.width, 0.0, 0.057 * dimensions.width, 0.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 0.08 * dimensions.height,
              ),
              CommonLoginWidgets.buildLoginImageSection(),
              CommonLoginWidgets.buildWelcomeSection(dimensions),
              SizedBox(
                height: 0.02 * dimensions.height,
              ),
              CommonLoginWidgets.buildMobileNumberSection(
                dimensions: dimensions,
                bloc: _bloc,
                mobileNumberController: _mobileNumberController,
                fillNumber: fillNumber,
              ),
              SizedBox(
                height: 0.005 * dimensions.height,
              ),
              CommonLoginWidgets.buildPrivacyPolicySection(settings),
              SizedBox(
                height: 0.04 * dimensions.height,
              ),
              CommonLoginWidgets.buildLoginButton(
                bloc: _bloc,
                isPrivacyPolicyAndTocChecked: isPrivacyPolicyAndTocChecked,
                mobileNumberController: _mobileNumberController,
                settings: settings,
                context: context,
                onNavigateToOtp: navigateToOtpScreen,
              ),
              SizedBox(
                height: 0.01 * dimensions.height,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void fillNumber(String mobileNumber) {
    _bloc.eventSink.add(EnterNumberEvent(mobileNumber: mobileNumber));
  }

  void navigateToOtpScreen(String phoneNumber, String phoneNumText,
      List<UserActivitySetting> settings) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OtpVerificationScreenProdRed(
          phoneNumber: phoneNumber,
          phoneNumText: phoneNumText,
          settings: settings,
        ),
      ),
    );
  }
}
