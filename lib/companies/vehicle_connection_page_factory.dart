import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';

import 'b2c/views/vehicle/vehicle_connection_page_b2c.dart';
import 'lapa/views/vehicle/vehicle_connection_page_lapa.dart';
import 'nds/views/vehicle/vehicle_connection_page_nds.dart';
import 'prodred/views/vehicle/vehicle_connection_page_prodred.dart';
import 'nichesolv/views/vehicle/vehicle_connection_page_nichesolv.dart';

class VehicleConnectionPageFactory {
  static Widget createVehicleConnectionPage() {
    switch (companyName) {
      case 'b2c':
        return const VehicleConnectionPageB2C();
      case 'lapa':
        return const VehicleConnectionPageLapa();
      case 'nds':
        return const VehicleConnectionPageNDS();
      case 'prodred':
        return const VehicleConnectionPageProdRed();
      case 'nichesolv':
        return const VehicleConnectionPageNichesolv();
      default:
        return const VehicleConnectionPageB2C();
    }
  }
}
