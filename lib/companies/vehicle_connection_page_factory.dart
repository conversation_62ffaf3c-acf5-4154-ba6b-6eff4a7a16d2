import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/widgets/vehicle/vehicle_connection_page_template_one_screen.dart';

/// Factory class to create company-specific vehicle connection page screens based on the current company configuration
class VehicleConnectionPageFactory {
  /// Creates and returns the appropriate vehicle connection page widget based on the company name
  static Widget createVehicleConnectionPage() {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getVehicleConnectionPageTemplate();
  }

  /// Returns the appropriate vehicle connection page template based on company validation requirements
  static Widget _getVehicleConnectionPageTemplate() {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return const VehicleConnectionPageTemplateOneScreen();
    }
  }

  /// Returns the company-specific vehicle connection page class name for debugging purposes
  static String getVehicleConnectionPageClassName() {
    return 'VehicleConnectionPageTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
