import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/b2c/views/select_fleet/select_fleet_b2c.dart';
import 'package:nds_app/companies/lapa/views/select_fleet/select_fleet_lapa.dart';
import 'package:nds_app/companies/nds/views/select_fleet/select_fleet_nds.dart';
import 'package:nds_app/companies/prodred/views/select_fleet/select_fleet_prodred.dart';
import 'package:nds_app/companies/nichesolv/views/select_fleet/select_fleet_nichesolv.dart';

class SelectFleetFactory {
  static Widget createSelectFleet() {
    switch (companyName) {
      case 'b2c':
        return const SelectFleetB2C();
      case 'lapa':
        return const SelectFleetLapa();
      case 'nds':
        return const SelectFleetNDS();
      case 'prodred':
        return const SelectFleetProdRed();
      case 'nichesolv':
        return const SelectFleetNichesolv();
      default:
        return const SelectFleetNDS(); // Default fallback
    }
  }
}
