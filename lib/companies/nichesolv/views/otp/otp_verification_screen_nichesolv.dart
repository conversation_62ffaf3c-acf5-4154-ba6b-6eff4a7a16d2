import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/otp/otp_bloc.dart';
import 'package:nds_app/blocs/otp/otp_events.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_event.dart';
import 'package:nds_app/blocs/sctoor_access/rider/user_vehicle/user_vehicle_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/constant/activity_type.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/enums/api_status.dart';
import 'package:nds_app/models/enums/otp_status.dart';
import 'package:nds_app/models/rider.dart';
import 'package:nds_app/models/user_activity_setting.dart';
import 'package:nds_app/screens/dashboard/dashboard.dart';
import '../../../../companies/splash_screen_factory.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/services/login_service.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import '../../../../branding/branding.dart';
import '../../../../widgets/common/custom_button.dart';

/// Nichesolv-specific OTP verification screen
/// Uses Nichesolv branding and can be customized for Nichesolv-specific OTP behavior
class OtpVerificationScreenNichesolv extends StatefulWidget {
  final String phoneNumber;
  final String phoneNumText;
  final List<UserActivitySetting> settings;

  const OtpVerificationScreenNichesolv({
    Key? key,
    required this.phoneNumber,
    required this.settings,
    required this.phoneNumText,
  }) : super(key: key);

  @override
  State<OtpVerificationScreenNichesolv> createState() =>
      _OtpVerificationScreenNichesolvState();
}

class _OtpVerificationScreenNichesolvState
    extends State<OtpVerificationScreenNichesolv> {
  final _bloc = OtpScreenBloc();
  late List<TextEditingController> _otpContainerControllers;
  late List<FocusNode> _otpContainerFocusNodes;
  late int maxTextFieldLength;
  bool isResendEnabled = false;
  int countdown = 10;
  Timer? timer;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': "Otp Verification Screen",
      'screen_class': widget.runtimeType.toString(),
    });

    maxTextFieldLength = 6;

    super.initState();
    widget.settings.add(UserActivitySetting(
        activityType: ActivityType.login,
        isNewSettingExist: false,
        message: "",
        value: ""));

    _otpContainerControllers = [];
    _otpContainerFocusNodes = [];
    for (int i = 0; i < _bloc.streams.length; i++) {
      _otpContainerControllers.add(TextEditingController());
      _otpContainerFocusNodes.add(FocusNode());
    }
    _otpContainerFocusNodes[0].requestFocus();
    startResendTimer();
  }

  bool isFoundError = false;

  void startResendTimer() {
    setState(() {
      isResendEnabled = false;
      countdown = 10;
    });

    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (countdown > 0) {
          countdown--;
        } else {
          isResendEnabled = true;
          timer.cancel();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return BlocListener<UserVehicleBloc, UserVehicleFetchState>(
      listener: (context, state) {
        if (isB2CUser && state.apiStatus == ApiStatus.success) {
          List<Rider> rider = context.read<UserVehicleBloc>().state.riders;
          bool isConnected = rider.any((element) => element.isConnected);

          // ignore: use_build_context_synchronously
          Navigator.pop(context);
          // ignore: use_build_context_synchronously
          Navigator.pushAndRemoveUntil(
            // ignore: use_build_context_synchronously
            context,
            MaterialPageRoute(
                builder: (context) => isConnected
                    ? const Dashboard()
                    : SplashScreenFactory.createPromotionalScreen()),
            (r) => false,
          );
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: Padding(
          padding: EdgeInsets.fromLTRB(0.057 * dimensions.width,
              0.077 * dimensions.width, 0.057 * dimensions.width, 0.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...[
                SizedBox(
                  height: 25 / 878 * dimensions.height,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.arrow_back_ios,
                    color: loginThemeColor,
                  ),
                ),
                SizedBox(
                  height: 0.038 * dimensions.height,
                ),
                Text(
                  otpVerificationScreen["text7"]!,
                  style: urbanistTextStyle(20 / 414 * dimensions.width,
                      loginThemeColor, FontWeight.w700),
                ),
                SizedBox(
                  height: 0.014 * dimensions.height,
                ),
                Row(
                  children: [
                    Expanded(
                        child: Text(
                      "${otpVerificationScreen["text8"]!}${widget.phoneNumText}",
                      style: urbanistTextStyle(14 / 414 * dimensions.width,
                          primaryBlueLight, FontWeight.w500),
                      softWrap: true,
                    ))
                  ],
                ),
                SizedBox(
                  height: 0.087 * dimensions.height,
                ),
              ],
              ...[getEnterOtpRowWithWidgets(dimensions)],
              ...[
                showErrorWidgetIf(
                  isVisible: false,
                  widget: SizedBox(height: 8 / 896 * dimensions.height),
                ),
                showErrorWidgetIf(
                  isVisible: false,
                  widget: Text(
                    "${otpVerificationScreen["text6"]!} ",
                    style: urbanistTextStyle(
                        12 / 414 * dimensions.width, colorRed, FontWeight.w500),
                  ),
                ),
                showErrorWidgetIf(
                  isVisible: false,
                  widget: SizedBox(height: 18 / 896 * dimensions.height),
                ),
                showErrorWidgetIf(
                  isVisible: true,
                  widget: SizedBox(height: 40 / 896 * dimensions.height),
                ),
              ],
              ...[
                Row(
                  children: [
                    isResendEnabled
                        ? Row(
                            children: [
                              Text(
                                "${otpVerificationScreen["text3"]!} ",
                                style: urbanistTextStyle(
                                  14 / 414 * MediaQuery.of(context).size.width,
                                  primaryBlueLight,
                                  FontWeight.w500,
                                ),
                              ),
                              GestureDetector(
                                onTap: () async {
                                  getCircularProgressIndicator(context);
                                  cleanTextControllers();
                                  await LoginService.sendLoginOtp(
                                      widget.phoneNumber);
                                  if (context.mounted) {
                                    Navigator.pop(context);
                                    startResendTimer();
                                  }
                                },
                                child: Text(
                                  otpVerificationScreen["text4"]!,
                                  style: urbanistTextStyle(
                                    14 /
                                        414 *
                                        MediaQuery.of(context).size.width,
                                    secondaryBlue,
                                    FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Text(
                            otpVerificationScreen["text9"]!
                                .replaceFirst("@value", '$countdown')
                                .toString(),
                            style: urbanistTextStyle(
                              12 / 414 * MediaQuery.of(context).size.width,
                              otpVerificationScreenGrey,
                              FontWeight.w500,
                            ),
                          ),
                  ],
                ),
                const Expanded(child: SizedBox()),
                StreamBuilder<String>(
                    stream: _bloc.otp,
                    initialData: "",
                    builder: (streamContext, snapshot) {
                      return snapshot.data!.length == _bloc.streams.length
                          ? GestureDetector(
                              onTap: () async {
                                getCircularProgressIndicator(context);

                                String? errorStatusCode =
                                    await LoginService.verifyLoginOtp(
                                        snapshot.data ?? "",
                                        widget.phoneNumber);

                                // ignore: use_build_context_synchronously
                                Navigator.pop(context);
                                if (errorStatusCode == null) {
                                  // ignore: use_build_context_synchronously
                                  getCircularProgressIndicator(context);

                                  for (UserActivitySetting e
                                      in widget.settings) {
                                    final request = {
                                      "activityType":
                                          e.activityType.requestName,
                                      "value": e.value,
                                      "organisationId": organisationId
                                    };

                                    http.Response response =
                                        await BackendApi.initiatePostCall(
                                      ApiUrls.saveUserKeyActivity,
                                      body: request,
                                    );

                                    if (response.statusCode != 200) {
                                      debugPrint(
                                          "user activity type ${e.activityType} failed for value ${e.value}");
                                    }
                                  }
                                  // ignore: use_build_context_synchronously
                                  context
                                      .read<UserVehicleBloc>()
                                      .add(LoadUserVehicleEvent());

                                  LogScreenTrackingEvent().logScreenView(
                                      eventName:
                                          trackingLabels['LoginAction']!);

                                  if (!isB2CUser) {
                                    // ignore: use_build_context_synchronously
                                    Navigator.pop(context);
                                    // ignore: use_build_context_synchronously
                                    Navigator.pushAndRemoveUntil(
                                      // ignore: use_build_context_synchronously
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              const Dashboard()),
                                      (r) => false,
                                    );
                                  }
                                } else {
                                  isFoundError = true;
                                  for (int i = 0;
                                      i < _otpContainerControllers.length;
                                      i++) {
                                    fillContainer(
                                        OtpStatus.invalid.toString(), i);
                                  }
                                }
                              },
                              child: CustomButton.gesture(
                                text: otpVerificationScreen["text5"]!,
                                backgroundColor: loginThemeColor,
                                size: CustomButtonSize.fullWidth,
                                textColor: colorWhite,
                                fontWeight: FontWeight.w600,
                              ))
                          : CustomButton.gesture(
                              text: otpVerificationScreen["text5"]!,
                              backgroundColor: loginThemeColor,
                              size: CustomButtonSize.fullWidth,
                              textColor: colorWhite,
                              fontWeight: FontWeight.w600,
                            );
                    }),
                SizedBox(
                  height: 0.01 * dimensions.height,
                )
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget showErrorWidgetIf({required bool isVisible, required Widget widget}) {
    return StreamBuilder<String>(
      stream: _bloc.streams[_otpContainerFocusNodes.length - 1],
      initialData: "",
      builder: (context, snapshot) {
        return Visibility(
            visible: (isVisible &&
                    snapshot.data != OtpStatus.invalid.toString()) ||
                (snapshot.data == OtpStatus.invalid.toString() && !isVisible),
            child: widget);
      },
    );
  }

  void fillContainer(String number, int index) {
    _bloc.eventSink.add(FillDigitEvent(digit: number, index: index));
    if (number.isEmpty) {
      _otpContainerFocusNodes[index + -1].requestFocus();
    } else if (index + 1 != _otpContainerFocusNodes.length) {
      _otpContainerFocusNodes[index + 1].requestFocus();
    }
    if (isFoundError && number != OtpStatus.invalid.toString()) {
      for (int i = 0; i < _otpContainerControllers.length; i++) {
        String num = _otpContainerControllers[i].text;
        if (i == index) {
          num = number;
        }
        _bloc.eventSink.add(FillDigitEvent(digit: num, index: i));
      }
      isFoundError = false;
    }
  }

  String? extractOtp(String? messageBody) {
    messageBody = messageBody ?? "";
    // Regular expression to find a 6-digit number
    final RegExp regExp = RegExp(r'\b\d{6}\b');
    final match = regExp.firstMatch(messageBody);
    return match?.group(0); // Return the matched OTP if found
  }

  getEnterOtpRowWithWidgets(Dimensions dimensions) {
    List<Widget> widgets = [];
    for (int i = 0; i < _otpContainerControllers.length; i++) {
      widgets.add(
        Expanded(
          child: StreamBuilder<String>(
              stream: _bloc.streams[i],
              initialData: "",
              builder: (context, snapshot) {
                _otpContainerControllers[i].selection = TextSelection(
                    baseOffset: _otpContainerControllers[i].text.length,
                    extentOffset: _otpContainerControllers[i].text.length);

                return Container(
                  decoration: BoxDecoration(
                      color: otpDigitContainerColor,
                      border: Border.all(
                          color: snapshot.data!.isNotEmpty
                              ? snapshot.data == OtpStatus.invalid.toString()
                                  ? colorRed
                                  : secondaryBlue
                              : unSelectedTextBoxColor,
                          width: 1.4),
                      borderRadius: BorderRadius.circular(8.0)),
                  height: 0.072 * dimensions.height,
                  child: Center(
                    child: TextField(
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                      ],
                      onTap: () {
                        _otpContainerControllers[i].selection = TextSelection(
                            baseOffset: _otpContainerControllers[i].text.length,
                            extentOffset:
                                _otpContainerControllers[i].text.length);
                      },
                      style: TextStyle(color: loginThemeColor),
                      autofillHints: i == 0 ? [AutofillHints.oneTimeCode] : [],
                      maxLength: maxTextFieldLength,
                      textAlign: TextAlign.center,
                      textAlignVertical: TextAlignVertical.center,
                      keyboardType: const TextInputType.numberWithOptions(
                          signed: true, decimal: true),
                      controller: _otpContainerControllers[i],
                      onChanged: (value) {
                        if (value.length == maxTextFieldLength) {
                          for (int index = 0; index < value.length; index++) {
                            _otpContainerControllers[index].text = value[index];
                            fillContainer(value[index], index);
                          }
                        } else {
                          fillContainer(value, i);
                        }
                      },
                      focusNode: _otpContainerFocusNodes[i],
                      decoration: InputDecoration(
                        counterText: "",
                        labelStyle: urbanistTextStyle(0.029 * dimensions.height,
                            colorBlack, FontWeight.w700),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                );
              }),
        ),
      );
      if (i + 1 < _otpContainerControllers.length) {
        widgets.add(
          SizedBox(width: 15 / 414 * dimensions.width),
        );
      }
    }
    return Row(children: widgets);
  }

  void cleanTextControllers() {
    for (var element in _otpContainerControllers) {
      element.clear();
    }
    _otpContainerFocusNodes.first.requestFocus();
  }

  @override
  void dispose() {
    cleanTextControllers();
    for (var e in _otpContainerControllers) {
      e.dispose();
    }
    for (var node in _otpContainerFocusNodes) {
      node.dispose();
    }
    timer?.cancel();
    super.dispose();
  }
}
