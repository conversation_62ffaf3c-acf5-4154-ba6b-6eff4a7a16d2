import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/vehicle_health_event.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import '../../../../../../common/colors.dart';
import '../../../../../../common/dimensions.dart';
import '../../../../../../common/image_urls.dart';
import '../../../../../../common/strings.dart';
import '../../../../../../common/text_styles.dart';
import '../../../../../../models/vehicle.dart';
import '../../../../../../utils/toast.dart';

class VehicleHealthPageNichesolv extends StatefulWidget {
  final String imei;
  final void Function() onBackPressed;
  final void Function(String partType, String partLabel) onHealthDetailPressed;
  final Vehicle vehicle;

  const VehicleHealthPageNichesolv({
    super.key,
    required this.imei,
    required this.onBackPressed,
    required this.onHealthDetailPressed,
    required this.vehicle,
  });

  @override
  State<VehicleHealthPageNichesolv> createState() => _VehicleHealthPageNichesolvState();
}

class _VehicleHealthPageNichesolvState extends State<VehicleHealthPageNichesolv> {
  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Vehicle Health Screen Nichesolv',
      'screen_class': widget.runtimeType.toString(),
    });
    super.initState();
  }

  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'excellent':
        return Color.fromRGBO(57, 224, 7, 1.0);
      case 'good':
        return Color.fromRGBO(65, 138, 7, 1.0);
      case 'normal':
        return Color.fromRGBO(57, 224, 7, 1.0);
      case 'high':
        return Color.fromRGBO(211, 123, 41, 1.0);
      case 'average':
        return Color.fromRGBO(211, 123, 41, 1.0);
      case 'poor':
      case 'bad':
        return Color.fromRGBO(211, 44, 41, 1.0);
      case 'low':
        return Color.fromRGBO(211, 44, 41, 1.0);
      default:
        return Color.fromRGBO(203, 211, 209, 1.0);
    }
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return BlocProvider(
      create: (_) => VehicleHealthBloc()..add(FetchVehicleHealth(widget.imei)),
      child: Scaffold(
        body: Padding(
          padding: isB2CUser ? EdgeInsets.all(16.0) : EdgeInsets.all(0),
          child: SafeArea(
            child: BlocBuilder<VehicleHealthBloc, VehicleHealthState>(
              builder: (context, state) {
                if (state is VehicleHealthLoading) {
                  return Center(
                    child: Image.asset(
                      isTwoWheels
                          ? loaderGifImages['2Wheels']!
                          : loaderGifImages['3Wheels']!,
                    ),
                  );
                } else if (state is VehicleHealthError) {
                  return Center(child: Text(vehicleScreen["noHealthData"]!));
                } else if (state is VehicleHealthLoaded) {
                  final images = state.profileImages.values.toList();
                  final parts = state.parts;
                  final PageController pageController = PageController();

                  // Add page change notification listener
                  final ValueNotifier<int> currentPageNotifier =
                      ValueNotifier<int>(0);
                  pageController.addListener(() {
                    if (pageController.page != null) {
                      final newPage = pageController.page!.round();
                      if (currentPageNotifier.value != newPage) {
                        currentPageNotifier.value = newPage;

                        // Preload next and previous images
                        if (newPage < images.length - 1) {
                          precacheImage(
                              NetworkImage(images[newPage + 1].url), context);
                        }
                        if (newPage > 0) {
                          precacheImage(
                              NetworkImage(images[newPage - 1].url), context);
                        }
                      }
                    }
                  });

                  // Preload first image when view is created
                  if (images.isNotEmpty) {
                    precacheImage(NetworkImage(images.first.url), context);
                    // Preload second image if available
                    if (images.length > 1) {
                      precacheImage(NetworkImage(images[1].url), context);
                    }
                  }

                  return Column(
                    children: [
                      Row(
                        children: [
                          IconButton(
                            onPressed: widget.onBackPressed,
                            icon: const Padding(
                              padding: EdgeInsets.only(
                                  right: 8.0, top: 8.0, bottom: 8.0),
                              child: Icon(
                                Icons.arrow_back_ios,
                                size: 18,
                              ),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            vehicleScreen["vehicleHealth"]!,
                            style: Theme.of(context).textTheme.headlineLarge,
                          ),
                        ],
                      ),
                      // Scrollable content
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Vehicle info
                              Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(16, 8, 16, 0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.vehicle.regNo,
                                      style: Theme.of(context)
                                          .textTheme
                                          .headlineLarge,
                                    ),
                                    Text(
                                      widget.vehicle.model,
                                      style:
                                          Theme.of(context).textTheme.bodySmall,
                                    ),
                                  ],
                                ),
                              ),
                              // Image carousel
                              SizedBox(
                                height: 425 / 896 * dimensions.height,
                                width: 425 / 414 * dimensions.width,
                                child: Stack(
                                  children: [
                                    PageView.builder(
                                      controller: pageController,
                                      itemCount: images.length,
                                      itemBuilder: (context, index) {
                                        final image = images[index];
                                        return LayoutBuilder(
                                            builder: (context, constraints) {
                                          // Calculate the scaling factors for the image
                                          final containerWidth =
                                              constraints.maxWidth;
                                          var containerHeight =
                                              425 / 896 * dimensions.height;

                                          // Calculate the scaled dimensions of the image
                                          var imageAspectRatio = 425 /
                                              896 *
                                              dimensions.height /
                                              425 /
                                              414 *
                                              dimensions.width;
                                          final containerAspectRatio =
                                              containerWidth / containerHeight;

                                          double scaledWidth;
                                          double scaledHeight;
                                          double offsetX = 0;
                                          double offsetY = 0;

                                          if (containerAspectRatio >
                                              imageAspectRatio) {
                                            // Height constrained, width has extra space
                                            scaledHeight = containerHeight;
                                            scaledWidth = containerHeight *
                                                imageAspectRatio;
                                            offsetX =
                                                (containerWidth - scaledWidth) /
                                                    2; // Center horizontally
                                          } else {
                                            // Width constrained, height has extra space
                                            scaledWidth = containerWidth;
                                            scaledHeight = containerWidth /
                                                imageAspectRatio;
                                            offsetY = (containerHeight -
                                                    scaledHeight) /
                                                2; // Center vertically
                                          }

                                          return Stack(
                                            children: [
                                              SizedBox(
                                                height: containerHeight,
                                                width: containerWidth,
                                                child: CachedNetworkImage(
                                                  imageUrl: image.url,
                                                  fit: BoxFit.contain,
                                                  placeholder: (context, url) =>
                                                      Center(
                                                    child: SizedBox(
                                                      width: 50,
                                                      height: 50,
                                                      child:
                                                          CircularProgressIndicator(
                                                        strokeWidth: 2.0,
                                                        valueColor:
                                                            AlwaysStoppedAnimation<
                                                                Color>(
                                                          Theme.of(context)
                                                              .primaryColor,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  errorWidget: (context, url,
                                                          error) =>
                                                      Icon(Icons.error,
                                                          color: Colors.red),
                                                  memCacheWidth:
                                                      containerWidth.toInt(),
                                                  memCacheHeight:
                                                      containerHeight.toInt(),
                                                ),
                                              ),
                                              ...image.coordinates.map((coord) {
                                                // Check if part exists in parts array
                                                final partExists = parts.any(
                                                    (p) =>
                                                        p.partType ==
                                                        coord.partLabel);

                                                // Calculate the proportional position within the scaled image
                                                final imageX = (coord.x /
                                                        418 /
                                                        414 *
                                                        dimensions.width) *
                                                    scaledWidth;
                                                final imageY = (coord.y /
                                                        400 /
                                                        896 *
                                                        dimensions.height) *
                                                    scaledHeight;

                                                // Add offsets to account for centering
                                                final actualX =
                                                    offsetX + imageX;
                                                final actualY =
                                                    offsetY + imageY;

                                                // If part doesn't exist, show exclamation icon
                                                if (!partExists) {
                                                  return Positioned(
                                                    left: actualX - 5,
                                                    top: actualY - 5,
                                                    child: GestureDetector(
                                                      onTap: () => {
                                                        CustomToast.error(
                                                            vehicleScreen[
                                                                "noPartData"]!)
                                                      },
                                                      child: Container(
                                                        height: 25 /
                                                            418 *
                                                            containerHeight,
                                                        width: 25 /
                                                            400 *
                                                            containerWidth,
                                                        decoration:
                                                            BoxDecoration(
                                                          shape:
                                                              BoxShape.circle,
                                                          color: Color.fromRGBO(
                                                              211, 44, 41, 1.0),
                                                          border: Border.all(
                                                              color:
                                                                  Colors.white,
                                                              width: 2),
                                                          boxShadow: [
                                                            BoxShadow(
                                                              color: Colors
                                                                  .black
                                                                  .withOpacity(
                                                                      0.2),
                                                              spreadRadius: 1,
                                                              blurRadius: 2,
                                                              offset:
                                                                  Offset(0, 1),
                                                            ),
                                                          ],
                                                        ),
                                                        child: const Center(
                                                          child: Text(
                                                            "!",
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize: 16,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }

                                                // Find part status for the coordinate
                                                final part = parts.firstWhere(
                                                  (p) =>
                                                      p.partType ==
                                                          coord.partLabel &&
                                                      (p.partLabel.contains(
                                                          coord.partLabel)),
                                                  orElse: () =>
                                                      parts.firstWhere(
                                                    (p) =>
                                                        p.partType ==
                                                        coord.partLabel,
                                                    orElse: () => parts.first,
                                                  ),
                                                );

                                                return Positioned(
                                                  left: actualX - 5,
                                                  top: actualY - 5,
                                                  child: GestureDetector(
                                                    onTap: () => widget
                                                        .onHealthDetailPressed(
                                                            coord.partType,
                                                            coord.partLabel),
                                                    child: Container(
                                                      height: 25 /
                                                          425 *
                                                          containerHeight,
                                                      width: 25 /
                                                          425 *
                                                          containerWidth,
                                                      decoration: BoxDecoration(
                                                        shape: BoxShape.circle,
                                                        color: getStatusColor(
                                                            part.status),
                                                        border: Border.all(
                                                            color: Colors.white,
                                                            width: 2),
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color: Colors.black
                                                                .withOpacity(
                                                                    0.2),
                                                            spreadRadius: 1,
                                                            blurRadius: 2,
                                                            offset:
                                                                Offset(0, 1),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              }).toList(),
                                            ],
                                          );
                                        });
                                      },
                                    ),
                                    // Next/Previous buttons
                                    Positioned(
                                      left: 0,
                                      top: 0,
                                      bottom: 0,
                                      child: Center(
                                        child: ValueListenableBuilder<int>(
                                            valueListenable:
                                                currentPageNotifier,
                                            builder: (context, currentPage, _) {
                                              final isFirstPage =
                                                  currentPage <= 0;

                                              return Container(
                                                margin: const EdgeInsets.only(
                                                    left: 12),
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: isFirstPage
                                                      ? Colors.grey
                                                          .withOpacity(0.3)
                                                      : Colors.white,
                                                  boxShadow: isFirstPage
                                                      ? []
                                                      : [
                                                          BoxShadow(
                                                            color: Colors.black
                                                                .withOpacity(
                                                                    0.2),
                                                            blurRadius: 6,
                                                            spreadRadius: 2,
                                                            offset:
                                                                const Offset(
                                                                    0, 1),
                                                          )
                                                        ],
                                                ),
                                                child: IconButton(
                                                  icon: Icon(
                                                    Icons.chevron_left,
                                                    color: isFirstPage
                                                        ? Colors.grey
                                                            .withOpacity(0.5)
                                                        : Colors.black54,
                                                    size: 30,
                                                  ),
                                                  onPressed: isFirstPage
                                                      ? null
                                                      : () {
                                                          pageController
                                                              .previousPage(
                                                            duration:
                                                                const Duration(
                                                                    milliseconds:
                                                                        300),
                                                            curve: Curves
                                                                .easeInOut,
                                                          );
                                                        },
                                                ),
                                              );
                                            }),
                                      ),
                                    ),
                                    Positioned(
                                      right: 0,
                                      top: 0,
                                      bottom: 0,
                                      child: Center(
                                        child: ValueListenableBuilder<int>(
                                            valueListenable:
                                                currentPageNotifier,
                                            builder: (context, currentPage, _) {
                                              final isLastPage = currentPage >=
                                                  images.length - 1;

                                              return Container(
                                                margin: const EdgeInsets.only(
                                                    right: 12),
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: isLastPage
                                                      ? Colors.grey
                                                          .withOpacity(0.3)
                                                      : Colors.white,
                                                  boxShadow: isLastPage
                                                      ? []
                                                      : [
                                                          BoxShadow(
                                                            color: Colors.black
                                                                .withOpacity(
                                                                    0.2),
                                                            blurRadius: 6,
                                                            spreadRadius: 2,
                                                            offset:
                                                                const Offset(
                                                                    0, 1),
                                                          )
                                                        ],
                                                ),
                                                child: IconButton(
                                                  icon: Icon(
                                                    Icons.chevron_right,
                                                    color: isLastPage
                                                        ? Colors.grey
                                                            .withOpacity(0.5)
                                                        : Colors.black54,
                                                    size: 30,
                                                  ),
                                                  onPressed: isLastPage
                                                      ? null
                                                      : () {
                                                          pageController
                                                              .nextPage(
                                                            duration:
                                                                const Duration(
                                                                    milliseconds:
                                                                        300),
                                                            curve: Curves
                                                                .easeInOut,
                                                          );
                                                        },
                                                ),
                                              );
                                            }),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: parts.length,
                                itemBuilder: (context, index) {
                                  final part = parts[index];

                                  return GestureDetector(
                                    onTap: () => widget.onHealthDetailPressed(
                                        part.partType, part.partType),
                                    child: Container(
                                      margin: EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .scaffoldBackgroundColor,
                                          borderRadius:
                                              BorderRadius.circular(8.0),
                                          border: Border.all(
                                              width: 1, color: colorGrey200),
                                          boxShadow: [
                                            BoxShadow(
                                                color: colorBlack
                                                    .withOpacity(0.25),
                                                offset: const Offset(1, 3),
                                                blurRadius: 3,
                                                spreadRadius: 1),
                                            BoxShadow(
                                                color: colorWhite
                                                    .withOpacity(0.25),
                                                offset: const Offset(-1, -3),
                                                blurRadius: 3,
                                                spreadRadius: 1)
                                          ]),
                                      child: Padding(
                                        padding: const EdgeInsets.all(12.0),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              part.partType,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .headlineMedium,
                                            ),
                                            Flexible(
                                              child: SizedBox(
                                                  width: 15.0 /
                                                      414 *
                                                      dimensions.width),
                                            ),
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 12,
                                                      vertical: 4),
                                              decoration: BoxDecoration(
                                                color:
                                                    getStatusColor(part.status),
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                              ),
                                              child: Text(
                                                part.status,
                                                style: poppinsTextStyle(
                                                    12 / 414 * dimensions.width,
                                                    colorWhite,
                                                    FontWeight.bold),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                              // Add some bottom padding
                              const SizedBox(height: 150),
                            ],
                          ),
                        ),
                      ),
                    ],
                  );
                }
                return const SizedBox();
              },
            ),
          ),
        ),
      ),
    );
  }
}
