import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/vehicle/ride_details/ride_details_toggle_bloc.dart';
import 'package:nds_app/blocs/vehicle/ride_details/ride_details_toggle_state.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/trip.dart';
import 'package:nds_app/screens/vehicle/overview_ride_detail_screen.dart';
import 'package:nds_app/screens/vehicle/route_screen.dart';
import 'package:nds_app/widgets/vehicle/ride_details_toggle_button.dart';

import '../../../../common/strings.dart';

class RideDetailsScreenNichesolv extends StatelessWidget {
  final Trip trip;
  final Color color;
  final ColorType colorType;

  const RideDetailsScreenNichesolv({
    super.key,
    required this.trip,
    required this.color,
    required this.colorType,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RideDetailsToggleBloc(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(
              rideDetailsScreenText["text1"]!,
            style: Theme.of(context).textTheme.headlineLarge,
          ),
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        ),
        body: Column(
          children: [
            RideDetailsToggleButton(
              color: color,
              colorType: colorType,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: BlocBuilder<RideDetailsToggleBloc, RideDetailsToggleState>(
                builder: (context, state) {
                  return state.isSwitchRight
                      ? RouteScreen(trip: trip)
                      : OverviewRideDetailScreen(
                          testId: trip.testId?.toString() ?? '');
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
