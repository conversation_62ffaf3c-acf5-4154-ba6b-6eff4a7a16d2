import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/widgets/cluster/body.dart';
import 'package:nds_app/widgets/cluster/bottom_menu.dart';
import 'package:nds_app/widgets/cluster/title_row.dart';

class ClusterNichesolv extends StatefulWidget {
  final VehicleInfo vehicleInfo;
  final String userName;

  const ClusterNichesolv({
    super.key,
    required this.vehicleInfo,
    required this.userName,
  });

  @override
  State<ClusterNichesolv> createState() => _ClusterNichesolvState();
}

class _ClusterNichesolvState extends State<ClusterNichesolv> {
  @override
  void initState() {
    super.initState();
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Cluster Screen Nichesolv',
      'screen_class': runtimeType.toString()
    });
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    bool isPortrait =
        MediaQuery.of(context).orientation == Orientation.portrait;
    return Scaffold(
      backgroundColor: colorGrey900,
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: isPortrait
                      ? 20
                      : 5 /
                          360 *
                          (isPortrait ? dimensions.width : dimensions.height),
                  vertical: isPortrait
                      ? 10
                      : 5 /
                          360 *
                          (isPortrait ? dimensions.width : dimensions.height)),
              child: ClusterTitleRow(
                isPortrait: isPortrait,
                userName: widget.userName,
              ),
            ),
            Expanded(
                child: ClusterBody(
              isPortrait: isPortrait,
              vehicleInfo: widget.vehicleInfo,
            )),
            ClusterBottomMenu(
              isPortrait: isPortrait,
            )
          ],
        ),
      ),
    );
  }
}
