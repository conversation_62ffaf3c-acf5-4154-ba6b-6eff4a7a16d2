import 'package:flutter/material.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/companies/about_vehicle_factory.dart';
import 'package:nds_app/companies/edit_profile_factory.dart';
import 'package:nds_app/companies/help_factory.dart';
import 'package:nds_app/companies/profile_details_factory.dart';
import 'package:nds_app/companies/profile_home_factory.dart';
import 'package:nds_app/companies/setting_factory.dart';
import 'package:nds_app/screens/vehicle/vehicleHealth/vehicle_health_detail_page.dart';
import 'package:nds_app/screens/vehicle/vehicleHealth/vehicle_health_page.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfileScreenNDS extends StatefulWidget {
  const ProfileScreenNDS({super.key});
  @override
  State<StatefulWidget> createState() {
    return _ProfileScreenNDSState();
  }
}

class _ProfileScreenNDSState extends State<ProfileScreenNDS> {
  late Widget activeScreen;
  String imei = "";
  Vehicle? vehicle;

  @override
  void initState() {
    activeScreen = ProfileHomeFactory.createProfileHome(
      openDetails: openProfileDetails,
      openHelp: openHelpPage,
      openSetting: openSetting,
      openAboutVehicle: openAboutVehiclePage,
    );
    _loadVehicleInfo();
    super.initState();
  }

  Future<void> _loadVehicleInfo() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    imei = pref.getString(connectedVehicleImeiNo) ?? "";
    // Create a basic Vehicle object with the required fields
    if (imei.isNotEmpty) {
      setState(() {
        vehicle = Vehicle(
          regNo: imei,
          model: pref.getString('vehicle_model') ?? "My Vehicle",
          images: [],
          soc: 0,
          distanceCovered: 0,
          startDate: DateTime.now().millisecondsSinceEpoch,
        );
      });
    }
  }

  void openProfileDetails() {
    setState(() {
      activeScreen = ProfileDetailsFactory.createProfileDetails(
          editProfile: openEditProfile, onBackPressed: onBackPressed);
    });
  }

  void openEditProfile() {
    setState(() {
      activeScreen = EditProfileFactory.createEditProfile(onBackPressed: openProfileDetails);
    });
  }

  void onBackPressed() {
    setState(() {
      activeScreen = ProfileHomeFactory.createProfileHome(
        openDetails: openProfileDetails,
        openHelp: openHelpPage,
        openSetting: openSetting,
        openAboutVehicle: openAboutVehiclePage,
      );
    });
  }

  void openSetting() {
    setState(() {
      activeScreen = SettingFactory.createSetting(onBackPressed: onBackPressed);
    });
  }

  void openHelpPage() {
    setState(() {
      activeScreen = HelpFactory.createHelp(onBackPressed: onBackPressed);
    });
  }

  void vehicleHealthPressed() {
    if (vehicle != null) {
      setState(() {
        activeScreen = VehicleHealthPage(
          imei: vehicle!.regNo,
          onBackPressed: vehicleHealthOnBackPressed,
          onHealthDetailPressed: vehicleHealthDetailPressed,
          vehicle: vehicle!,
        );
      });
    }
  }

  void vehicleHealthOnBackPressed() {
    setState(() {
      activeScreen = AboutVehicleFactory.createAboutVehicle(
        onBackPressed: onBackPressed,
        healthPressed: vehicleHealthPressed,
      );
    });
  }

  void vehicleHealthDetailPressed(String partType, String partLabel) {
    if (vehicle != null) {
      setState(() {
        activeScreen = VehicleHealthDetailPage(
          onBackPressed: vehicleHealthDetailOnBackPressed,
          imei: vehicle!.regNo,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle!,
        );
      });
    }
  }

  void vehicleHealthDetailOnBackPressed() {
    if (vehicle != null) {
      setState(() {
        activeScreen = VehicleHealthPage(
          imei: vehicle!.regNo,
          onBackPressed: vehicleHealthOnBackPressed,
          onHealthDetailPressed: vehicleHealthDetailPressed,
          vehicle: vehicle!,
        );
      });
    }
  }

  void openAboutVehiclePage() {
    setState(() {
      activeScreen = AboutVehicleFactory.createAboutVehicle(
        onBackPressed: onBackPressed,
        healthPressed: vehicleHealthPressed,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Padding(
        padding: EdgeInsets.only(top: 0 * dimensions.height),
        child: activeScreen);
  }
}
