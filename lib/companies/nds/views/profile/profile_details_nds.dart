import 'package:flutter/material.dart';
import 'package:nds_app/blocs/profile/profile_details_stream.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/utils/format_date.dart';
import 'package:nds_app/widgets/profile/profile_image.dart';
import '../../../../common/constant.dart';
import '../../../../common/image_urls.dart';
import '../../../../widgets/profile/edit_profile_item.dart';

class ProfileDetailsNDS extends StatefulWidget {
  final void Function() onBackPressed;
  final void Function() editProfile;

  const ProfileDetailsNDS(
      {super.key, required this.editProfile, required this.onBackPressed});

  @override
  State<ProfileDetailsNDS> createState() => _ProfileDetailsNDSState();
}

class _ProfileDetailsNDSState extends State<ProfileDetailsNDS> {
  late void Function() onBackPressed;
  late void Function() editProfile;
  final stream = ProfileStream();

  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  final TextEditingController _genderController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _mobileNumberController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();
  final TextEditingController _bloodGroupController = TextEditingController();
  final TextEditingController _emergencyNumberController =
      TextEditingController();

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Profile Details Screen NDS',
      'screen_class': widget.runtimeType.toString(),
    });

    onBackPressed = widget.onBackPressed;
    editProfile = widget.editProfile;
    stream.fetchProfileDetails();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    //Color color = colorGrey800;
    Color bgColor = colorGrey100;
    ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();
    if (themeMode == ThemeMode.dark) {
      //color = colorWhite;
      bgColor = colorBackgroundDarkMode;
    }

    return StreamBuilder<Map<String, dynamic>>(
        stream: stream.profileStream,
        builder: (context, snapshot) {
          Widget widget =  Center(
            child:  Image.asset(
              isTwoWheels ?
              loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
            ),
          );

          if (snapshot.connectionState != ConnectionState.waiting) {
            Map<String, dynamic>? profile = snapshot.data;

            _firstNameController.text = profile?["firstName"] ?? "";
            _lastNameController.text = profile?["lastName"] ?? "";
            _dobController.text =
                profile?["dob"] == null ? "-" : formatDate(profile?["dob"]);
            _genderController.text =
                profile?["sex"] == null ? "-" : "${profile?["sex"]}";
            _emailController.text =
                profile?["email"] == null ? "-" : "${profile?["email"]}";
            _mobileNumberController.text = profile?["mobileNumber"] == null
                ? "-"
                : "${profile?["mobileNumber"]}";
            _addressController.text = profile?["longAddress"] == null
                ? "-"
                : "${profile?["longAddress"]}";
            _weightController.text = profileScreen["weight_in_kg"]!.replaceAll(
                "@weight",
                profile?["weightInKg"] == null
                    ? "-"
                    : "${double.parse(profile!["weightInKg"]!.toString()).round()}");

            _bloodGroupController.text = profile?["bloodGroup"] == null
                ? "-"
                : "${profile?["bloodGroup"]}";
            _emergencyNumberController.text =
                profile?["emergencyNumber"] == null
                    ? "-"
                    : "${profile?["emergencyNumber"]}";
            widget = Scaffold(
              body: SingleChildScrollView(
                child: Stack(
                  clipBehavior: Clip.none,
                  alignment: Alignment.topCenter,
                  children: [
                    Image.asset(
                      profileScreenImages["profileBg"]!, //profileBg
                      fit: BoxFit.fill,
                      width: double.infinity,
                    ),
                    Container(
                      margin: const EdgeInsets.only(
                        top: 150,
                      ),
                      decoration:  BoxDecoration(
                        color: bgColor,
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(20),
                          topLeft: Radius.circular(20),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            SizedBox(
                              height: 0.1 * dimensions.width,
                            ),
                            // First Name
                            EditProfileItem(
                              title: profileScreen["titleFirstName"]!,
                              hint: profileScreen["hint1"]!,
                              controller: _firstNameController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 20,
                              isTextFieldEnabled: false,
                            ),
                            // Last Name
                            EditProfileItem(
                              title: profileScreen["titleLastName"]!,
                              hint: profileScreen["hint2"]!,
                              controller: _lastNameController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 20,
                              isTextFieldEnabled: false,
                            ),
                            // DOB
                            EditProfileItem(
                              title: profileScreen["detail2"]!,
                              hint: profile?["dob"] == null
                                  ? "-"
                                  : formatDate(profile?["dob"]),
                              controller: _dobController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 80,
                              isTextFieldEnabled: false,
                            ),
                            // Gender
                            EditProfileItem(
                              title: profileScreen["detail3"]!,
                              hint: profile?["sex"] == null
                                  ? "-"
                                  : "${profile?["sex"]}",
                              controller: _genderController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 20,
                              isTextFieldEnabled: false,
                            ),
                            // Email
                            EditProfileItem(
                              title: profileScreen["detail4"]!,
                              hint: profile?["email"] == null
                                  ? "-"
                                  : "${profile?["email"]}",
                              controller: _emailController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 20,
                              isTextFieldEnabled: false,
                            ),
                            // Mobile Number
                            EditProfileItem(
                              title: profileScreen["detail5"]!,
                              hint: profile?["mobileNumber"] == null
                                  ? "-"
                                  : "${profile?["mobileNumber"]}",
                              controller: _mobileNumberController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 15,
                              isTextFieldEnabled: false,
                            ),
                            // Address
                            EditProfileItem(
                              title: profileScreen["detail7"]!,
                              hint: profile?["longAddress"] == null
                                  ? "-"
                                  : "${profile?["longAddress"]}",
                              controller: _addressController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 100,
                              isTextFieldEnabled: false,
                            ),
                            // Weight
                            EditProfileItem(
                              title: profileScreen["detail8"]!,
                              hint: profileScreen["weight_in_kg"]!.replaceAll(
                                  "@weight",
                                  profile?["weightInKg"] == null
                                      ? "-"
                                      : "${double.parse(profile!["weightInKg"]!.toString()).round()}"),
                              controller: _weightController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 20,
                              isTextFieldEnabled: false,
                            ),
                            // Blood Group
                            EditProfileItem(
                              title: profileScreen["detail9"]!,
                              hint: profile?["bloodGroup"] == null
                                  ? "-"
                                  : "${profile?["bloodGroup"]}",
                              controller: _bloodGroupController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 20,
                              isTextFieldEnabled: false,
                            ),
                            // Emergency Number
                            EditProfileItem(
                              title: profileScreen["detail10"]!,
                              hint: profile?["emergencyNumber"] == null
                                  ? "-"
                                  : "${profile?["emergencyNumber"]}",
                              controller: _emergencyNumberController,
                              onTap: () {},
                              keyboardType: TextInputType.text,
                              maxLength: 15,
                              isTextFieldEnabled: false,
                            ),
                            const SizedBox(
                              height: 40,
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                        top: 0.2 * dimensions.width,
                      ),
                      child: GestureDetector(
                        onTap: editProfile,
                        child: Stack(
                          clipBehavior: Clip.none,
                          alignment: Alignment.center,
                          children: [
                            ClipOval(
                              child: ProfileImage(
                                imageUrl: profile?["profileImageUrl"],
                                isHomeProfile: false,
                              ),
                            ),
                            Positioned(
                              bottom: -15,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: colorGrey800,
                                  borderRadius: BorderRadius.circular(50),
                                  border:
                                      Border.all(color: colorWhite, width: 2),
                                ),
                                height: 44,
                                width: 44,
                                child: const Icon(
                                  Icons.edit_outlined,
                                  size: 20,
                                  color: colorWhite,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          GestureDetector(
                            onTap: onBackPressed,
                            child: const Icon(
                              Icons.arrow_back_ios,
                              size: 15,
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            profileScreen["title2"]!,
                            style: Theme.of(context).textTheme.headlineLarge,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
          return widget;
        });
  }
}
