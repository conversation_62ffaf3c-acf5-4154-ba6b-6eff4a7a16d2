import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nds_app/blocs/profile/profile_details_stream.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/utils/on_will_pop_popup.dart';
import 'package:nds_app/widgets/profile/profile_image.dart';
import 'package:nds_app/widgets/profile/scooter_access/scooter_access_screen.dart';

class ProfileHomeNDS extends StatefulWidget {
  final void Function() openDetails;
  final void Function() openHelp;
  final void Function() openSetting;
  final void Function() openAboutVehicle;

  const ProfileHomeNDS({
    super.key,
    required this.openDetails,
    required this.openHelp,
    required this.openSetting,
    required this.openAboutVehicle,
  });

  @override
  State<ProfileHomeNDS> createState() => _ProfileHomeNDSState();
}

class _ProfileHomeNDSState extends State<ProfileHomeNDS> {
  late void Function() openDetails;
  late void Function() openHelp;
  late void Function() openSetting;
  late void Function() openAboutVehicle;
  late void Function() openScooterAccess;

  final stream = ProfileStream();

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Profile Screen NDS',
      'screen_class': widget.runtimeType.toString(),
    });

    openDetails = widget.openDetails;
    openHelp = widget.openHelp;
    openSetting = widget.openSetting;
    openAboutVehicle = widget.openAboutVehicle;
    stream.fetchProfileDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return StreamBuilder<Map<String, dynamic>>(
      stream: stream.profileStream,
      builder: (context, snapshot) {
        Widget widget =  Center(
          child:  Image.asset(
            isTwoWheels ?
            loaderGifImages['2Wheels']! : loaderGifImages['3Wheels']!,
          ),
        );

        if (snapshot.connectionState != ConnectionState.waiting) {
          debugPrint("+++++ snapshot = ${snapshot.data}");
          Map<String, dynamic>? profile = snapshot.data;
          widget = Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(profileScreen["title1"]!,
                    style: Theme.of(context).textTheme.headlineLarge),
                const SizedBox(
                  height: 35,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ProfileImage(imageUrl: profile?["profileImageUrl"], isHomeProfile: true,),
                    SizedBox(
                      width: 14 / 414 * dimensions.width,
                    ),
                    Expanded(
                      child: Text(
                          "${profile?["firstName"]} ${profile?["lastName"]}",
                          style: Theme.of(context).textTheme.bodyMedium),
                    )
                  ],
                ),
                const SizedBox(
                  height: 55,
                ),
                RowItemNDS(
                  image: profileScreenImages["menu1"]!,
                  text: profileScreen["menu1"]!,
                  onTapHandler: openDetails,
                ),
                const Divider(
                  height: 1,
                  color: colorGrey800,
                ),
                Visibility(
                  visible: false,
                  child: Column(
                    children: [
                      RowItemNDS(
                        image: profileScreenImages["menu2"]!,
                        text: profileScreen["menu2"]!,
                        onTapHandler: () {},
                      ),
                      const Divider(
                        height: 1,
                        color: colorGrey800,
                      ),
                    ],
                  ),
                ),
                Visibility(
                  visible: isNDSUser,
                  child: Column(
                    children: [
                      RowItemNDS(
                        image: profileScreenImages["menu7"]!,
                        text: profileScreen["menu7"]!,
                        onTapHandler: () async {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ScooterAccessScreen(),
                            ),
                          );
                        },
                      ),
                      const Divider(
                        height: 1,
                        color: colorGrey800,
                      ),
                    ],
                  ),
                ),
                RowItemNDS(
                  image: profileScreenImages["menu3"]!,
                  text: profileScreen["menu3"]!,
                  onTapHandler: openSetting,
                ),
                const Divider(
                  height: 1,
                  color: colorGrey800,
                ),
                RowItemNDS(
                  image: profileScreenImages["menu4"]!,
                  text: profileScreen["menu4"]!,
                  onTapHandler: openHelp,
                ),
                const Divider(
                  height: 1,
                  color: colorGrey800,
                ),
                Visibility(
                  visible: isNDSUser,
                  child: Column(
                    children: [
                      RowItemNDS(
                        image: profileScreenImages["menu6"]!,
                        text: profileScreen["menu6"]!,
                        onTapHandler: openAboutVehicle,
                      ),
                      const Divider(
                        height: 1,
                        color: colorGrey800,
                      ),
                    ],
                  ),
                ),
                RowItemNDS(
                  image: profileScreenImages["menu5"]!,
                  text: profileScreen["menu8"]!,
                  onTapHandler: () async {
                    onWillPopUpMsg(
                      context,
                      DialogAction.deleteUserAccount,
                    );
                  },
                ),
                const Divider(
                  height: 1,
                  color: colorGrey800,
                ),
                RowItemNDS(
                  image: profileScreenImages["menu5"]!,
                  text: profileScreen["menu5"]!,
                  onTapHandler: () async {
                    onWillPopUpMsg(
                      context,
                      DialogAction.logout,
                    );
                  },
                ),
                SizedBox(
                  height: 100 / 896 * dimensions.height,
                ),
              ],
            )),
          );
        }

        return widget;
      },
    );
  }
}

class RowItemNDS extends StatelessWidget {
  final String image;
  final String text;
  final void Function() onTapHandler;

  const RowItemNDS(
      {super.key,
      required this.image,
      required this.text,
      required this.onTapHandler});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTapHandler,
      child: Container(
        padding: const EdgeInsets.only(
            top: 35, right: 19.01, bottom: 15, left: 15.33),
        decoration: const BoxDecoration(color: Colors.transparent),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                SizedBox(
                  height: 13.33,
                  width: 13.33,
                  child: SvgPicture.asset(
                    image,
                    fit: BoxFit.fill,
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
                Text(
                  text,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
              ],
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 15,
            )
          ],
        ),
      ),
    );
  }
}
