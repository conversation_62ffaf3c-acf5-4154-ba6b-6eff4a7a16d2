import 'package:flutter/material.dart';
import 'package:nds_app/companies/b2c/views/insights/insights_b2c.dart';
import 'package:nds_app/companies/lapa/views/insights/insights_lapa.dart';
import 'package:nds_app/companies/nds/views/insights/insights_nds.dart';
import 'package:nds_app/companies/nichesolv/views/insights/insights_nichesolv.dart';
import 'package:nds_app/companies/prodred/views/insights/insights_prodred.dart';
import 'package:nds_app/models/enums/color_type.dart';
import '../branding/branding.dart';

/// Factory class to create company-specific insights screens based on the current company configuration
class InsightsFactory {

  /// Creates and returns the appropriate insights widget based on the company name
  static Widget createInsights(Color color, ColorType colorType) {
    switch (companyName) {
      case 'b2c':
        return InsightsB2C(color: color, colorType: colorType);
      case 'lapa':
        return InsightsLapa(color: color, colorType: colorType);
      case 'nds':
        return InsightsNDS(color: color, colorType: colorType);
      case 'prodred':
        return InsightsProdRed(color: color, colorType: colorType);
      case 'Nichesolv':
        return InsightsNichesolv(color: color, colorType: colorType);
      default:
        // Default to NDS if company is not recognized
        return InsightsNDS(color: color, colorType: colorType);
    }
  }

  /// Returns the company-specific insights class name for debugging purposes
  static String getInsightsClassName() {
    switch (companyName) {
      case 'b2c':
        return 'InsightsB2C';
      case 'lapa':
        return 'InsightsLapa';
      case 'nds':
        return 'InsightsNDS';
      case 'prodred':
        return 'InsightsProdRed';
      case 'Nichesolv':
        return 'InsightsNichesolv';
      default:
        return 'InsightsNDS';
    }
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'Nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
