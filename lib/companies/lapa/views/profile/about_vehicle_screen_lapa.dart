import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/about_vehicle.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/utils/oval_shape_vehicle_base.dart';
import 'package:nds_app/widgets/profile/vehicle_specifications.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../common/image_urls.dart';
import 'package:cached_network_image/cached_network_image.dart';

class AboutVehicleScreenLapa extends StatefulWidget {
  final void Function() onBackPressed;
  final void Function() healthPressed;
  const AboutVehicleScreenLapa(
      {super.key, required this.onBackPressed, required this.healthPressed});

  @override
  State<AboutVehicleScreenLapa> createState() => _AboutVehicleScreenLapaState();
}

class _AboutVehicleScreenLapaState extends State<AboutVehicleScreenLapa> {
  AboutVehicle aboutVehicle = AboutVehicle();
  late Function() onBackPressed;
  late Color color = colorGrey800;
  late Future loadColor;
  String? imageUrl;

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'About Vehicle Screen Lapa',
      'screen_class': widget.runtimeType.toString(),
    });
    loadColor = loadColorFromSharedPreferences();
    onBackPressed = widget.onBackPressed;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    void Function() goVehicleHealth = widget.healthPressed;

    return FutureBuilder(
        future: loadData(),
        builder: (context, snapshot) {
          Widget widget = Center(
            child: Image.asset(
              isTwoWheels
                  ? loaderGifImages['2Wheels']!
                  : loaderGifImages['3Wheels']!,
            ),
          );

          if (snapshot.connectionState == ConnectionState.done) {
            widget = SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        GestureDetector(
                          onTap: onBackPressed,
                          child: const Padding(
                            padding: EdgeInsets.all(12.0),
                            child: Icon(
                              Icons.arrow_back_ios,
                              size: 15,
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Text(
                          profileScreen["menu6"]!,
                          style: Theme.of(context).textTheme.headlineLarge,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 24 / 896 * dimensions.height,
                    ),
                    SizedBox(
                      height: 200 / 896 * dimensions.height,
                      width: 374 / 414 * dimensions.width,
                      child: Row(
                        children: [
                          SizedBox(
                            height: 200 / 896 * dimensions.height,
                            width: 181 / 414 * dimensions.width,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: 145 / 896 * dimensions.height,
                                  width: 181 / 414 * dimensions.width,
                                  child: Stack(
                                    children: [
                                      getOvalShapeBaseForVehicle(
                                          dimensions,
                                          aboutVehicle.vehicleColorHexCode !=
                                                  null
                                              ? aboutVehicle
                                                  .vehicleColorHexCode!
                                                  .toColor()
                                              : colorBlack800),
                                      getVehicleImage(dimensions, imageUrl),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 16 / 896 * dimensions.height,
                                ),
                                Text(aboutVehicle.modelName ?? "-",
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineMedium),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 200 / 896 * dimensions.height,
                            width: 193 / 414 * dimensions.width,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                getVehicleDetailsRow(
                                    dimensions,
                                    profileScreen["aboutVehicle1"]!,
                                    aboutVehicle.regNo ?? "",
                                    context),
                                getVehicleDetailsRow(
                                    dimensions,
                                    profileScreen["aboutVehicle2"]!,
                                    aboutVehicle.vehicleManufacturerName ?? "-",
                                    context),
                                getVehicleDetailsRow(
                                    dimensions,
                                    profileScreen["aboutVehicle3"]!,
                                    profileScreen["aboutVehicleValue1"]!
                                        .replaceFirst(
                                            "@value1",
                                            aboutVehicle
                                                .rideModesMaxRange!.values
                                                .reduce((a, b) => a < b ? a : b)
                                                .round()
                                                .toString())
                                        .replaceFirst(
                                            "@value2",
                                            aboutVehicle
                                                .rideModesMaxRange!.values
                                                .reduce((a, b) => a > b ? a : b)
                                                .round()
                                                .toString()),
                                    context),
                                getVehicleDetailsRow(
                                    dimensions,
                                    profileScreen["aboutVehicle4"]!,
                                    profileScreen["aboutVehicleValue2"]!
                                        .replaceFirst(
                                            "@value",
                                            (aboutVehicle.netWeight ?? "-")
                                                .toString()),
                                    context),
                                getVehicleDetailsRow(
                                    dimensions,
                                    profileScreen["aboutVehicle5"]!,
                                    aboutVehicle.colorName ?? "-",
                                    context),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 14 / 896 * dimensions.height,
                    ),
                    VehicleSpecifications(
                        aboutVehicle: aboutVehicle, imageUrl: imageUrl ?? ""),
                    SizedBox(
                      height: 14 / 896 * dimensions.height,
                    ),
                    Visibility(
                      visible: !isProdRedUser,
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(32, 8, 32, 8),
                        child: SizedBox(
                          width: double.infinity,
                          height: 56,
                          child: ElevatedButton(
                            onPressed: () async {
                              goVehicleHealth();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: color,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(45.0),
                              ),
                              elevation: 4,
                            ),
                            child: Text(
                              vehicleScreen["vehicleHealth"]!,
                              style: const TextStyle(
                                color: colorWhite,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 100 / 896 * dimensions.height,
                    ),
                  ],
                ),
              ),
            );
          }
          return widget;
        });
  }

  Row getVehicleDetailsRow(
      Dimensions dimensions, String key, String value, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(key,
            style: Theme.of(context)
                .textTheme
                .displaySmall
                ?.copyWith(fontWeight: FontWeight.w800)),
        Flexible(
            child:
                Text(value, style: Theme.of(context).textTheme.displaySmall)),
      ],
    );
  }

  Container getContactContaner(String imagePath, String title, String content,
      Dimensions dimensions, BuildContext context) {
    return Container(
      height: 81 / 896 * dimensions.height,
      width: 374 / 414 * dimensions.width,
      padding: EdgeInsets.all(16 / 414 * dimensions.width),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8 / 414 * dimensions.width),
          border: Border.all(
            color: Theme.of(context).highlightColor.withOpacity(0.25),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(2, 2))
          ]),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 48 / 896 * dimensions.height,
            width: 48 / 414 * dimensions.width,
            child: Image.asset(imagePath),
          ),
          SizedBox(
            width: 8 / 414 * dimensions.width,
          ),
          FittedBox(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: Theme.of(context).textTheme.labelSmall),
                Text(content, style: Theme.of(context).textTheme.displayMedium),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget getVehicleImage(Dimensions dimensions, String? imageUrl) {
    return Positioned(
        left: 12 / 414 * dimensions.width,
        child: SizedBox(
          height: 140 / 896 * dimensions.height,
          width: 128 / 414 * dimensions.width,
          child: imageUrl == null
              ? Icon(Icons.electric_bike, size: 128 / 414 * dimensions.width)
              : CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.fill,
                  memCacheWidth: 250,
                  maxWidthDiskCache: 250,
                  fadeInDuration: const Duration(milliseconds: 100),
                  placeholder: (context, url) => const SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(strokeWidth: 2.0),
                  ),
                  errorWidget: (context, url, error) => Icon(
                      Icons.electric_bike,
                      size: 128 / 414 * dimensions.width),
                ),
        ));
  }

  Widget getOvalShapeBaseForVehicle(Dimensions dimensions, Color vehicleColor) {
    return Positioned(
        top: 100 / 896 * dimensions.height,
        child: Column(
          children: [
            SizedBox(
              height: 45 / 896 * dimensions.height,
              width: 152 / 414 * dimensions.width,
              child: OvalShapeBase(
                width: 152 / 414 * dimensions.width,
                height: 45 / 896 * dimensions.height,
                gradientColors: [colorWhite, vehicleColor],
              ),
            ),
          ],
        ));
  }

  loadData() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    String imei = pref.getString(connectedVehicleImeiNo) ?? "";
    JsonDecoder decoder = const JsonDecoder();

    http.Response aboutVehicleResponse = await BackendApi.initiateGetCall(
        ApiUrls.aboutVehicle,
        params: {"imei": imei});

    Map<String, dynamic> aboutVehicleMap =
        decoder.convert(aboutVehicleResponse.body);

    aboutVehicle = AboutVehicle.fromJson(aboutVehicleMap);

    aboutVehicle.images?.forEach(
      (element) {
        if (element.tag == defaultImageTag) {
          imageUrl = element.url!;
        }
      },
    );
  }

  loadColorFromSharedPreferences() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    String hexColorInStr = pref.getString(vehicleThemeColorInHex) ?? "";
    setState(() {
      color = hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
    });
  }
}
