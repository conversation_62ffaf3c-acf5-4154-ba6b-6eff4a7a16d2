import 'package:flutter/material.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/companies/about_vehicle_factory.dart';
import 'package:nds_app/companies/edit_profile_factory.dart';
import 'package:nds_app/companies/help_factory.dart';
import 'package:nds_app/companies/profile_details_factory.dart';
import 'package:nds_app/companies/profile_home_factory.dart';
import 'package:nds_app/companies/setting_factory.dart';
import 'package:nds_app/screens/vehicle/vehicleHealth/vehicle_health_detail_page.dart';
import 'package:nds_app/screens/vehicle/vehicleHealth/vehicle_health_page.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfileScreenLapa extends StatefulWidget {
  const ProfileScreenLapa({super.key});
  @override
  State<StatefulWidget> createState() {
    return _ProfileScreenLapaState();
  }
}

class _ProfileScreenLapaState extends State<ProfileScreenLapa> {
  late Widget activeScreen;
  String imei = "";
  Vehicle? vehicle;

  @override
  void initState() {
    activeScreen = ProfileHomeFactory.createProfileHome(
      openDetails: openProfileDetails,
      openHelp: openHelpPage,
      openSetting: openSetting,
      openAboutVehicle: openAboutVehiclePage,
    );
    _loadVehicleInfo();
    super.initState();
  }

  Future<void> _loadVehicleInfo() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    imei = pref.getString(connectedVehicleImeiNo) ?? "";
    // Create a basic Vehicle object with the required fields
    if (imei.isNotEmpty) {
      setState(() {
        vehicle = Vehicle(
          regNo: imei,
          model: pref.getString('vehicle_model') ?? "My Vehicle",
          images: [],
          soc: 0,
          distanceCovered: 0.0,
          startDate: DateTime.now().millisecondsSinceEpoch,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: activeScreen,
      ),
    );
  }

  void openProfileDetails() {
    setState(() {
      activeScreen = ProfileDetailsFactory.createProfileDetails(
        onBackPressed: openProfileHome,
        editProfile: openEditProfile,
      );
    });
  }

  void openEditProfile() {
    setState(() {
      activeScreen = EditProfileFactory.createEditProfile(
        onBackPressed: openProfileDetails,
      );
    });
  }

  void openHelpPage() {
    setState(() {
      activeScreen = HelpFactory.createHelp(
        onBackPressed: openProfileHome,
      );
    });
  }

  void openSetting() {
    setState(() {
      activeScreen = SettingFactory.createSetting(
        onBackPressed: openProfileHome,
      );
    });
  }

  void openAboutVehiclePage() {
    setState(() {
      activeScreen = AboutVehicleFactory.createAboutVehicle(
        onBackPressed: openProfileHome,
        healthPressed: openVehicleHealthPage,
      );
    });
  }

  void openVehicleHealthPage() {
    if (vehicle != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VehicleHealthPage(
            imei: vehicle!.regNo,
            vehicle: vehicle!,
            onBackPressed: () {
              Navigator.pop(context);
            },
            onHealthDetailPressed: (String partType, String partLabel) {
              openVehicleHealthDetailPage(partType, partLabel);
            },
          ),
        ),
      );
    }
  }

  void openVehicleHealthDetailPage(String partType, String partLabel) {
    if (vehicle != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VehicleHealthDetailPage(
            imei: vehicle!.regNo,
            vehicle: vehicle!,
            partType: partType,
            partLabel: partLabel,
            onBackPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      );
    }
  }

  void openProfileHome() {
    setState(() {
      activeScreen = ProfileHomeFactory.createProfileHome(
        openDetails: openProfileDetails,
        openHelp: openHelpPage,
        openSetting: openSetting,
        openAboutVehicle: openAboutVehiclePage,
      );
    });
  }
}
