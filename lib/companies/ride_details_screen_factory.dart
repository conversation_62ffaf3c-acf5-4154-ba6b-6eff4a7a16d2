import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/trip.dart';

import 'b2c/views/vehicle/ride_details_screen_b2c.dart';
import 'lapa/views/vehicle/ride_details_screen_lapa.dart';
import 'nds/views/vehicle/ride_details_screen_nds.dart';
import 'prodred/views/vehicle/ride_details_screen_prodred.dart';
import 'nichesolv/views/vehicle/ride_details_screen_nichesolv.dart';

class RideDetailsScreenFactory {
  static Widget createRideDetailsScreen({
    required Trip trip,
    required Color color,
    required ColorType colorType,
  }) {
    switch (companyName) {
      case 'b2c':
        return RideDetailsScreenB2C(trip: trip, color: color, colorType: colorType);
      case 'lapa':
        return RideDetailsScreenLapa(trip: trip, color: color, colorType: colorType);
      case 'nds':
        return RideDetailsScreenNDS(trip: trip, color: color, colorType: colorType);
      case 'prodred':
        return RideDetailsScreenProdRed(trip: trip, color: color, colorType: colorType);
      case 'nichesolv':
        return RideDetailsScreenNichesolv(trip: trip, color: color, colorType: colorType);
      default:
        return RideDetailsScreenB2C(trip: trip, color: color, colorType: colorType);
    }
  }
}
