import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/models/trip.dart';
import 'package:nds_app/companies/widgets/vehicle/base_ride_details_screen.dart';
import 'package:nds_app/companies/widgets/vehicle/ride_details_screen_template_one_screen.dart';

/// Factory class to create company-specific ride details screens based on the current company configuration
class RideDetailsScreenFactory {
  /// Creates and returns the appropriate ride details widget based on the company name
  static Widget createRideDetailsScreen({
    required Trip trip,
    required Color color,
    required ColorType colorType,
  }) {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getRideDetailsScreenTemplate(
      trip: trip,
      color: color,
      colorType: colorType,
    );
  }

  /// Returns the appropriate ride details template based on company validation requirements
  static BaseRideDetailsScreen _getRideDetailsScreenTemplate({
    required Trip trip,
    required Color color,
    required ColorType colorType,
  }) {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return RideDetailsScreenTemplateOneScreen(
            trip: trip, color: color, colorType: colorType);
    }
  }

  /// Returns the company-specific ride details class name for debugging purposes
  static String getRideDetailsScreenClassName() {
    return 'RideDetailsScreenTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
