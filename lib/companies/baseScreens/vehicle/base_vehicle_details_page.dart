import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle.dart';

/// Base class for vehicle details page screens
/// This serves as a contract for all vehicle details page template implementations
abstract class BaseVehicleDetailsPage extends StatefulWidget {
  final Vehicle vehicle;
  final void Function() onBackPressed;
  final void Function() healthPressed;

  const BaseVehicleDetailsPage({
    super.key,
    required this.vehicle,
    required this.onBackPressed,
    required this.healthPressed,
  });
}
