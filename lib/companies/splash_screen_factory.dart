import 'package:flutter/material.dart';

// Template imports
import 'package:nds_app/companies/widgets/login/base_login.dart';
import 'package:nds_app/companies/widgets/login/login_template_one_screen.dart';
import 'package:nds_app/companies/widgets/loading/loading_template_one_screen.dart';
import 'package:nds_app/companies/widgets/splash/splash_template_one_screen.dart';
import 'package:nds_app/companies/widgets/onboarding/onboarding_template_one_screen.dart';
import 'package:nds_app/companies/widgets/otp/otp_template_one_screen.dart';
import 'package:nds_app/companies/widgets/promotional/promotional_template_one_screen.dart';
import 'package:nds_app/companies/widgets/promotional/promotional_template_two_screen.dart';

// Branding
import '../branding/branding.dart';
import 'package:nds_app/models/user_activity_setting.dart';

/// Factory class to create company-specific splash screens based on the current company configuration
class SplashScreenFactory {
  /// Creates and returns the appropriate splash screen widget based on the company name
  /// All companies currently use SplashTemplateOneScreen
  static Widget createSplashScreen() {
    // All companies use the same splash template
    return const SplashTemplateOneScreen();
  }

  /// Creates and returns the appropriate loading screen widget based on the company name
  /// All companies currently use LoadingTemplateOneScreen
  static Widget createLoadingScreen() {
    // All companies use the same loading template
    return const LoadingTemplateOneScreen();
  }

  /// Creates and returns the appropriate onboarding screen widget based on the company name
  /// All companies currently use OnboardingTemplateOneScreen
  static Widget createOnboardingScreen() {
    // All companies use the same onboarding template
    return const OnboardingTemplateOneScreen();
  }

  static Widget createPromotionalScreen() {
    switch (companyName) {
      case 'b2c': case 'lapa':
        return const PromotionalTemplateTwoScreen();
      default:
        // All other companies use template one with factory-based navigation
        return const PromotionalTemplateOneScreen();
    }
  }

  static BaseLogin createLoginScreen(
      {required List<UserActivitySetting> settings}) {
    // All companies use the same login template for now
    return LoginTemplateOneScreen(settings: settings);
  }

  static Widget createOtpScreen({
    required String phoneNumber,
    required String phoneNumText,
    required List<UserActivitySetting> settings,
  }) {
    // All companies use the same OTP template
    return OtpTemplateOneScreen(
      phoneNumber: phoneNumber,
      phoneNumText: phoneNumText,
      settings: settings,
    );
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'Nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
