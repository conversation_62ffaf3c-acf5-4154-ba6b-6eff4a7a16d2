import 'package:flutter/material.dart';
import 'package:nds_app/models/user_info.dart';
import 'package:nds_app/widgets/dashboard/home/<USER>';

/// Factory class for ScanAndFuelSaving - now uses the reusable component
/// All companies use the same ScanAndFuelSaving implementation
class ScanAndFuelSavingFactory {
  static Widget createScanAndFuelSaving({
    required Function() action,
    required UserInfo? userInfo,
  }) {
    // All companies now use the same reusable ScanAndFuelSaving component
    return ScanAndFuelSaving(action: action, userInfo: userInfo);
  }
}
