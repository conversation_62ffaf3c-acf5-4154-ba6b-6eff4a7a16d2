import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/b2c/views/connect_vehicle/connect_vehicle_b2c.dart';
import 'package:nds_app/companies/lapa/views/connect_vehicle/connect_vehicle_lapa.dart';
import 'package:nds_app/companies/nds/views/connect_vehicle/connect_vehicle_nds.dart';
import 'package:nds_app/companies/prodred/views/connect_vehicle/connect_vehicle_prodred.dart';
import 'package:nds_app/companies/nichesolv/views/connect_vehicle/connect_vehicle_nichesolv.dart';
import 'package:nds_app/screens/dashboard/connect_vehicle.dart';

class ConnectVehicleFactory {
  static Widget createConnectVehicle() {
    switch (companyName) {
      case 'b2c':
        return const ConnectVehicleB2C();
      case 'lapa':
        return const ConnectVehicleLapa();
      case 'nds':
        return const ConnectVehicleNDS();
      case 'prodred':
        return const ConnectVehicleProdRed();
      case 'nichesolv':
        return const ConnectVehicleNichesolv();
      default:
        return const ConnectVehicle();
    }
  }
}
