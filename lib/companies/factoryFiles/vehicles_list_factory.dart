import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/companies/baseScreens/vehicle/base_vehicles_list.dart';
import 'package:nds_app/companies/widgets/vehicle/vehicles_list_template_one_screen.dart';

/// Factory class to create company-specific vehicles list screens based on the current company configuration
class VehiclesListFactory {
  /// Creates and returns the appropriate vehicles list widget based on the company name
  static Widget createVehiclesList({
    required void Function(Vehicle vehicle) switchScreen,
  }) {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getVehiclesListTemplate(switchScreen: switchScreen);
  }

  /// Returns the appropriate vehicles list template based on company validation requirements
  static BaseVehiclesList _getVehiclesListTemplate({
    required void Function(Vehicle vehicle) switchScreen,
  }) {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return VehiclesListTemplateOneScreen(switchScreen);
    }
  }

  /// Returns the company-specific vehicles list class name for debugging purposes
  static String getVehiclesListClassName() {
    return 'VehiclesListTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
