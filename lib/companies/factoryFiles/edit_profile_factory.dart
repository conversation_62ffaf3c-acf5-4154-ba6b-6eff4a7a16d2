import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/baseScreens/profile_screens/base_profile_screens.dart';
import 'package:nds_app/companies/widgets/profile_screens/edit_profile_template_one_screen.dart';

/// Factory class to create company-specific edit profile screens based on the current company configuration
class EditProfileFactory {
  /// Creates and returns the appropriate edit profile widget based on the company name
  static Widget createEditProfile({
    required void Function() onBackPressed,
  }) {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getEditProfileTemplate(onBackPressed: onBackPressed);
  }

  /// Returns the appropriate edit profile template based on company validation requirements
  static BaseEditProfileScreen _getEditProfileTemplate({
    required void Function() onBackPressed,
  }) {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return EditProfileTemplateOneScreen(onBackPressed: onBackPressed);
    }
  }

  /// Returns the company-specific edit profile class name for debugging purposes
  static String getEditProfileClassName() {
    return 'EditProfileTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
