import 'package:flutter/material.dart';
import 'package:nds_app/companies/widgets/connect_vehicle/connect_vehicle_template_one_screen.dart';

/// Factory class to create connect vehicle screens using template-based architecture
class ConnectVehicleFactory {
  /// Creates and returns the connect vehicle template screen
  /// All companies use the same connect vehicle template since functionality is identical
  static Widget createConnectVehicle() {
    // All companies use the same connect vehicle template
    return const ConnectVehicleTemplateOneScreen();
  }
}
