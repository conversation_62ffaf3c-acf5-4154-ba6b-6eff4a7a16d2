import 'package:flutter/material.dart';
import 'package:nds_app/models/vehicle_info.dart';
import 'package:nds_app/companies/widgets/cluster/cluster_template_one_screen.dart';

/// Factory class to create cluster screens using template-based architecture
class ClusterFactory {
  /// Creates and returns the cluster template screen
  /// All companies use the same cluster template since functionality is identical
  static Widget createCluster({
    required VehicleInfo vehicleInfo,
    required String userName,
  }) {
    // All companies use the same cluster template
    return ClusterTemplateOneScreen(
        vehicleInfo: vehicleInfo, userName: userName);
  }
}
