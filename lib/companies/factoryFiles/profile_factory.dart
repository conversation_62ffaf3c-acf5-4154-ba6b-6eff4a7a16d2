import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/widgets/profile/base_profile.dart';
import 'package:nds_app/companies/widgets/profile/profile_template_one_screen.dart';

/// Factory class to create company-specific profile screens based on the current company configuration
class ProfileFactory {
  /// Creates and returns the appropriate profile widget based on the company name
  static Widget createProfile() {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getProfileTemplate();
  }

  /// Returns the appropriate profile template based on company validation requirements
  static BaseProfile _getProfileTemplate() {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return const ProfileTemplateOneScreen();
    }
  }

  /// Returns the company-specific profile class name for debugging purposes
  static String getProfileClassName() {
    return 'ProfileTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
