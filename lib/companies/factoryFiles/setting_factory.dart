import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/baseScreens/profile_screens/base_profile_screens.dart';
import 'package:nds_app/companies/widgets/profile_screens/setting_template_one_screen.dart';

/// Factory class to create company-specific setting screens based on the current company configuration
class SettingFactory {
  /// Creates and returns the appropriate setting widget based on the company name
  static Widget createSetting({
    required void Function() onBackPressed,
  }) {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getSettingTemplate(onBackPressed: onBackPressed);
  }

  /// Returns the appropriate setting template based on company validation requirements
  static BaseSettingScreen _getSettingTemplate({
    required void Function() onBackPressed,
  }) {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return SettingTemplateOneScreen(onBackPressed: onBackPressed);
    }
  }

  /// Returns the company-specific setting class name for debugging purposes
  static String getSettingClassName() {
    return 'SettingTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
