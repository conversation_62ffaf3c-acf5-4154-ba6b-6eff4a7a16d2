import 'package:flutter/material.dart';

import 'package:nds_app/companies/widgets/trip_history/trip_history_list_template_one_screen.dart';

/// Factory class to create trip history list screens using template-based architecture
class TripHistoryListFactory {
  /// Creates and returns the trip history list template screen
  /// All companies use the same trip history list template since functionality is identical
  static Widget createTripHistoryList() {
    // All companies use the same trip history list template
    return const TripHistoryListTemplateOneScreen();
  }

  /// Returns the company-specific trip history list class name for debugging purposes
  static String getTripHistoryListClassName() {
    return 'TripHistoryListTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().isNotEmpty; // All companies supported
  }
}
