import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';

import 'b2c/views/profile/profile_details_b2c.dart';
import 'lapa/views/profile/profile_details_lapa.dart';
import 'nds/views/profile/profile_details_nds.dart';
import 'prodred/views/profile/profile_details_prodred.dart';
import 'nichesolv/views/profile/profile_details_nichesolv.dart';

class ProfileDetailsFactory {
  static Widget createProfileDetails({
    required void Function() onBackPressed,
    required void Function() editProfile,
  }) {
    switch (companyName) {
      case 'b2c':
        return ProfileDetailsB2C(
          onBackPressed: onBackPressed,
          editProfile: editProfile,
        );
      case 'lapa':
        return ProfileDetailsLapa(
          onBackPressed: onBackPressed,
          editProfile: editProfile,
        );
      case 'nds':
        return ProfileDetailsNDS(
          onBackPressed: onBackPressed,
          editProfile: editProfile,
        );
      case 'prodred':
        return ProfileDetailsProdRed(
          onBackPressed: onBackPressed,
          editProfile: editProfile,
        );
      case 'nichesolv':
        return ProfileDetailsNichesolv(
          onBackPressed: onBackPressed,
          editProfile: editProfile,
        );
      default:
        return ProfileDetailsB2C(
          onBackPressed: onBackPressed,
          editProfile: editProfile,
        );
    }
  }
}
