import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';

import 'b2c/views/vehicle/vehicleHealth/vehicle_health_detail_page_b2c.dart';
import 'lapa/views/vehicle/vehicleHealth/vehicle_health_detail_page_lapa.dart';
import 'nds/views/vehicle/vehicleHealth/vehicle_health_detail_page_nds.dart';
import 'prodred/views/vehicle/vehicleHealth/vehicle_health_detail_page_prodred.dart';
import 'nichesolv/views/vehicle/vehicleHealth/vehicle_health_detail_page_nichesolv.dart';

class VehicleHealthDetailPageFactory {
  static Widget createVehicleHealthDetailPage({
    required void Function() onBackPressed,
    required String imei,
    required String partType,
    required String partLabel,
    required Vehicle vehicle,
  }) {
    switch (companyName) {
      case 'b2c':
        return VehicleHealthDetailPageB2C(
          onBackPressed: onBackPressed,
          imei: imei,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle,
        );
      case 'lapa':
        return VehicleHealthDetailPageLapa(
          onBackPressed: onBackPressed,
          imei: imei,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle,
        );
      case 'nds':
        return VehicleHealthDetailPageNDS(
          onBackPressed: onBackPressed,
          imei: imei,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle,
        );
      case 'prodred':
        return VehicleHealthDetailPageProdRed(
          onBackPressed: onBackPressed,
          imei: imei,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle,
        );
      case 'nichesolv':
        return VehicleHealthDetailPageNichesolv(
          onBackPressed: onBackPressed,
          imei: imei,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle,
        );
      default:
        return VehicleHealthDetailPageB2C(
          onBackPressed: onBackPressed,
          imei: imei,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle,
        );
    }
  }
}
