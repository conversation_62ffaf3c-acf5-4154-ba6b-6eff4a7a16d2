import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/vehicle.dart';
import 'package:nds_app/companies/widgets/vehicle_health/base_vehicle_health_detail.dart';
import 'package:nds_app/companies/widgets/vehicle_health/vehicle_health_detail_template_one_screen.dart';

/// Factory class to create company-specific vehicle health detail screens based on the current company configuration
class VehicleHealthDetailPageFactory {
  /// Creates and returns the appropriate vehicle health detail widget based on the company name
  static Widget createVehicleHealthDetailPage({
    required void Function() onBackPressed,
    required String imei,
    required String partType,
    required String partLabel,
    required Vehicle vehicle,
  }) {
    // All companies currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getVehicleHealthDetailTemplate(
      onBackPressed: onBackPressed,
      imei: imei,
      partType: partType,
      partLabel: partLabel,
      vehicle: vehicle,
    );
  }

  /// Returns the appropriate vehicle health detail template based on company validation requirements
  static BaseVehicleHealthDetail _getVehicleHealthDetailTemplate({
    required void Function() onBackPressed,
    required String imei,
    required String partType,
    required String partLabel,
    required Vehicle vehicle,
  }) {
    // Currently all companies use template one
    // Future: Add company-specific validation checks here if needed
    switch (companyName) {
      case 'b2c':
      case 'lapa':
      case 'nds':
      case 'prodred':
      case 'nichesolv':
      default:
        return VehicleHealthDetailTemplateOneScreen(
          onBackPressed: onBackPressed,
          imei: imei,
          partType: partType,
          partLabel: partLabel,
          vehicle: vehicle,
        );
    }
  }

  /// Returns the company-specific vehicle health detail class name for debugging purposes
  static String getVehicleHealthDetailClassName() {
    return 'VehicleHealthDetailTemplateOneScreen';
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
