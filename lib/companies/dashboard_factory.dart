import 'package:flutter/material.dart';
import 'package:nds_app/companies/widgets/dashboard/dashboard_template_one_screen.dart';

/// Factory class to create dashboard screens using template-based architecture
class DashboardFactory {
  /// Creates and returns the dashboard template screen
  /// All companies use the same dashboard template since functionality is identical
  /// The branding system automatically handles company-specific logos and assets
  static Widget createDashboard() {
    // All companies use the same dashboard template
    return const DashboardTemplateOneScreen();
  }
}
