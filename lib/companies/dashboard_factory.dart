import 'package:flutter/material.dart';
import 'package:nds_app/companies/b2c/views/dashboard/dashboard_b2c.dart';
import 'package:nds_app/companies/lapa/views/dashboard/dashboard_lapa.dart';
import 'package:nds_app/companies/nds/views/dashboard/dashboard_nds.dart';
import 'package:nds_app/companies/nichesolv/views/dashboard/dashboard_nichesolv.dart';
import 'package:nds_app/companies/prodred/views/dashboard/dashboard_prodred.dart';
import '../branding/branding.dart';

/// Factory class to create company-specific dashboard screens based on the current company configuration
class DashboardFactory {

  /// Creates and returns the appropriate dashboard widget based on the company name
  /// The branding system automatically handles company-specific logos and assets
  static Widget createDashboard() {
    switch (companyName) {
      case 'b2c':
        return const DashboardB2C();
      case 'lapa':
        return const DashboardLapa();
      case 'nds':
        return const DashboardNDS();
      case 'prodred':
        return const DashboardProdRed();
      case 'nichesolv':
        return const DashboardNichesolv();
      default:
        // Default to NDS if company is not recognized
        return const DashboardNDS();
    }
  }

  /// Returns the company-specific dashboard class name for debugging purposes
  static String getDashboardClassName() {
    switch (companyName) {
      case 'b2c':
        return 'DashboardB2C';
      case 'lapa':
        return 'DashboardLapa';
      case 'nds':
        return 'DashboardNDS';
      case 'prodred':
        return 'DashboardProdRed';
      case 'nichesolv':
        return 'DashboardNichesolv';
      default:
        return 'DashboardNDS';
    }
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
