import 'package:flutter/material.dart';
import 'package:nds_app/widgets/common/custom_button.dart';
import 'package:nds_app/blocs/navigation/navigation_bar_stream.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/screens/Login/login.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/widgets/common/progress_indicator.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<bool> onWillPopUpMsg(
  BuildContext context,
  DialogAction dialogAction,
) async {
  Dimensions dimensions = Dimensions(context);
  return ((await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
                16 / 414 * MediaQuery.of(context).size.width),
          ),
          titlePadding: EdgeInsets.only(
              top: 8 / 896 * dimensions.height,
              left: 20 / 414 * dimensions.width),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(dialogAction.title,
                      style: Theme.of(context).textTheme.bodyMedium),
                  IconButton(
                    padding: EdgeInsets.only(right: 2 / 414 * dimensions.width),
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  )
                ],
              ),
              SizedBox(
                height: 8 / 896 * dimensions.height,
              ),
              Text(dialogAction.content,
                  style: Theme.of(context).textTheme.headlineMedium),
            ],
          ),
          content: SizedBox(
            height: 4 / 896 * dimensions.height,
          ),
          actions: <Widget>[
            Row(
              children: [
                Expanded(
                  child: CustomButton.text(
                    onPressed: () async {
                      getCircularProgressIndicator(context);
                      initiateRoutingOfApp(dialogAction, context);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                            8 / 414 * MediaQuery.of(context).size.width),
                        border: Border.all(width: 1, color: colorGrey400),
                      ),
                      height: dimensions.height * 40 / 896,
                      // width: dimensions.width * 115 / 414,
                      child: Center(
                        child: Text(commonText['labelYes']!,
                            textScaler: TextScaler.noScaling,
                            style: Theme.of(context).textTheme.displayMedium),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: CustomButton.text(
                    onPressed: () {
                      Navigator.of(context).pop(false);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                            8 / 414 * MediaQuery.of(context).size.width),
                        border: Border.all(width: 1, color: colorGrey500),
                        color: colorBlack,
                      ),
                      height: dimensions.height * 40 / 896,
                      //  width: dimensions.width * 115 / 414,
                      child: Center(
                        child: Text(commonText['labelNo']!,
                            textScaler: TextScaler.noScaling,
                            style: Theme.of(context)
                                .textTheme
                                .displayMedium
                                ?.copyWith(color: colorGrey25)),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      )) ??
      false);
}

initiateRoutingOfApp(DialogAction dialogAction, BuildContext context) async {
  Widget? widget;

  if (dialogAction == DialogAction.logout ||
      dialogAction == DialogAction.deleteUserAccount) {
    widget = LoginScreen(
      settings: [
        privacyPolicySetting!,
        termsAndConditionsSetting!,
      ],
    );
    // ignore: use_build_context_synchronously
    await dialogAction.action(context: context);
    NavigationBarStream().submitIndex(0);

    SharedPreferences pref = await SharedPreferences.getInstance();

    pref.clear();
    resetVariables();

    sharedPreferences = await SharedPreferences.getInstance();
    // ignore: use_build_context_synchronously
    Navigator.of(context).pop();

    // ignore: use_build_context_synchronously
    MyApp.of(context).changeTheme(ThemeMode.light);

    LogScreenTrackingEvent()
        .logScreenView(eventName: trackingLabels['LogoutAction']!);
  }

  Navigator.pushAndRemoveUntil(
      // ignore: use_build_context_synchronously
      context,
      MaterialPageRoute(builder: (context) => widget!),
      (r) => false);
}
